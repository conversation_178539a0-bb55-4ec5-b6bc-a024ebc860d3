"""基于 RapidOCR 的图像文档加载器

该模块提供使用 RapidOCR 引擎从图像文件中提取文本内容的功能。
需要安装 rapidocr_onnxruntime 库，使用前请执行 pip install rapidocr_onnxruntime

典型使用示例:
    loader = RapidOCRLoader(file_path="example.jpg")
    docs = loader.load()
"""

from typing import List
from langchain.document_loaders import UnstructuredFileLoader

class RapidOCRLoader(UnstructuredFileLoader):
    """使用 RapidOCR 处理图像文件的文档加载器
    
    继承自 UnstructuredFileLoader，专门用于解析图像文件中的文本内容。
    支持常见图像格式如 JPG、PNG、BMP 等。
    
    属性:
        file_path: 要加载的图像文件路径
        unstructured_kwargs: 传递给底层文本分区器的参数
    """

    def _get_elements(self) -> List:
        """解析图像文件并返回结构化元素列表
        
        该方法会执行以下操作:
        1. 使用 RapidOCR 进行光学字符识别
        2. 将识别结果拼接为连续文本
        3. 使用 unstructured 库进行文本分区
        
        返回:
            包含文档元素的列表，每个元素代表一个文本段落或结构块
        """
        def img2text(filepath: str) -> str:
            """执行图像到文本的转换
            
            参数:
                filepath: 要处理的图像文件路径
                
            返回:
                从图像中提取的拼接文本字符串
                
            抛出:
                ImportError: 当 rapidocr_onnxruntime 未安装时抛出
            """
            from rapidocr_onnxruntime import RapidOCR
            
            # 初始化 OCR 引擎（默认使用中英文模型）
            ocr = RapidOCR()
            resp = ""
            
            # 执行 OCR 并处理结果
            result, _ = ocr(filepath)  # 第二个返回值是可选调试信息，此处忽略
            if result:
                # 提取每行识别结果中的文本内容（结果格式为 [[坐标], 文本, 置信度]）
                ocr_result = [line[1] for line in result]
                resp += "\n".join(ocr_result)
            
            return resp

        # 执行 OCR 获取原始文本
        text = img2text(self.file_path)
        
        # 使用 unstructured 库进行文本分区处理
        from unstructured.partition.text import partition_text
        return partition_text(text=text, **self.unstructured_kwargs)


if __name__ == "__main__":
    # 测试示例：加载示例图片并打印识别结果
    loader = RapidOCRLoader(file_path="tests\samples\ocr_test.jpg")
    docs = loader.load()
    print(docs)
