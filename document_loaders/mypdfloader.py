"""基于 RapidOCR 的 PDF 文档加载器

该模块提供使用 RapidOCR 引擎从 PDF 文件中提取文本内容的功能。
需要安装 rapidocr_onnxruntime 库，使用前请执行 pip install rapidocr_onnxruntime

典型使用示例:
    loader = RapidOCRPDFLoader(file_path="example.pdf")
    docs = loader.load()
"""

from typing import List
from langchain.document_loaders.unstructured import UnstructuredFileLoader
import tqdm

class RapidOCRPDFLoader(UnstructuredFileLoader):
    """使用 RapidOCR 处理 PDF 文件的文档加载器

    继承自 UnstructuredFileLoader，专门用于解析 PDF 文件中的文本和图像内容。
    """

    def _get_elements(self) -> List:
        """解析 PDF 文件并返回结构化元素列表

        该方法会执行以下操作:
        1. 使用 pyMuPDF 读取 PDF 文件的每一页
        2. 提取每页的文本内容
        3. 使用 RapidOCR 识别每页中的图像内容
        4. 将识别结果拼接为连续文本
        5. 使用 unstructured 库进行文本分区

        返回:
            包含文档元素的列表，每个元素代表一个文本段落或结构块
        """
        def pdf2text(filepath):
            """执行 PDF 到文本的转换

            参数:
                filepath: 要处理的 PDF 文件路径

            返回:
                从 PDF 中提取的拼接文本字符串
            """
            import fitz  # pyMuPDF里面的fitz包，不要与pip install fitz混淆
            from rapidocr_onnxruntime import RapidOCR
            import numpy as np

            ocr = RapidOCR()  # 初始化 OCR 引擎
            doc = fitz.open(filepath)  # 打开 PDF 文件
            resp = ""  # 初始化响应字符串

            # 初始化进度条，显示当前处理的页码
            b_unit = tqdm.tqdm(total=doc.page_count, desc="RapidOCRPDFLoader context page index: 0")
            for i, page in enumerate(doc):
                # 更新进度条描述为当前页码
                b_unit.set_description("RapidOCRPDFLoader context page index: {}".format(i))
                b_unit.refresh()

                # 提取当前页的文本内容
                text = page.get_text("")
                resp += text + "\n"

                # 提取当前页的所有图像
                img_list = page.get_images()
                for img in img_list:
                    # 将图像转换为像素矩阵
                    pix = fitz.Pixmap(doc, img[0])
                    img_array = np.frombuffer(pix.samples, dtype=np.uint8).reshape(pix.height, pix.width, -1)
                    # 使用 OCR 识别图像中的文本
                    result, _ = ocr(img_array)
                    if result:
                        # 提取 OCR 识别结果中的文本内容
                        ocr_result = [line[1] for line in result]
                        resp += "\n".join(ocr_result)

                # 更新进度
                b_unit.update(1)
            return resp

        # 执行 PDF 到文本的转换
        text = pdf2text(self.file_path)

        # 使用 unstructured 库进行文本分区处理
        from unstructured.partition.text import partition_text
        return partition_text(text=text, **self.unstructured_kwargs)

if __name__ == "__main__":
    # 测试示例：加载示例 PDF 并打印识别结果
    loader = RapidOCRPDFLoader(file_path="tests\samples\ocr_pdf_test.pdf")
    docs = loader.load()
    print(docs)
