import shutup
shutup.please()

import sys
sys.path.append(".")  # 将当前目录加入 Python 搜索路径，便于 import

from server.knowledge_base.migrate import (
    create_tables,      # 创建数据库表
    reset_tables,       # 重置数据库表（清空/删除并重新创建）
    folder2db,          # 将本地文件夹中的文档索引到数据库中
    prune_db_docs,      # 清理数据库中已不存在于本地的文档
    prune_folder_files, # 清理本地文件夹中已在数据库中删除的文档
)
from configs.model_config import NLTK_DATA_PATH

# 设置 nltk 的数据存放路径，以便使用 nltk 的分词/停用词等功能
import nltk
nltk.data.path = [NLTK_DATA_PATH] + nltk.data.path

from datetime import datetime
import sys


if __name__ == "__main__":
    import argparse

    # 创建一个命令行参数解析器，用于解析用户在命令行中传入的选项或参数
    parser = argparse.ArgumentParser(
        description="please specify only one operate method once time."
    )

    # -r 或 --recreate-vs：如果已经在本地文件夹中放置了一些文档，但是数据库还没有向量信息，
    # 或者使用的 EMBEDDING_MODEL / DEFAUL_VS_TYPE 发生变更，需要“重置并重新生成”向量索引
    parser.add_argument(
        "-r",
        "--recreate-vs",
        action="store_true",
        help=(
            '''
            recreate vector store.
            use this option if you have copied document files to the content folder, 
            but vector store has not been populated or DEFAUL_VS_TYPE/EMBEDDING_MODEL changed.
            '''
        )
    )

    # -u 或 --update-in-db：重新为“已经在数据库中存在的文档”更新向量信息
    # 即跳过仅在本地存在而数据库中没有的文件
    parser.add_argument(
        "-u",
        "--update-in-db",
        action="store_true",
        help=(
            '''
            update vector store for files exist in database.
            use this option if you want to recreate vectors for files exist in db and 
            skip files exist in local folder only.
            '''
        )
    )

    # -i 或 --increament：增量模式，为“仅在本地文件夹中存在但不在数据库中的文档”创建向量
    parser.add_argument(
        "-i",
        "--increament",
        action="store_true",
        help=(
            '''
            update vector store for files exist in local folder and not exist in database.
            use this option if you want to create vectors increamentally.
            '''
        )
    )

    # --prune-db：对数据库做“修剪”操作，删除数据库中已经在本地文件夹中不存在的文档
    parser.add_argument(
        "--prune-db",
        action="store_true",
        help=(
            '''
            delete docs in database that not existed in local folder.
            it is used to delete database docs after user deleted some doc files in file browser
            '''
        )
    )

    # --prune-folder：对本地文件夹做“修剪”操作，删除本地文件夹中没有对应数据库记录的文档
    parser.add_argument(
        "--prune-folder",
        action="store_true",
        help=(
            '''
            delete doc files in local folder that not existed in database.
            is is used to free local disk space by delete unused doc files.
            '''
        )
    )

    # --kb-name：指定要操作的知识库名称（或列表），默认为空列表即对所有在 KB_ROOT_PATH 下的文件夹进行操作
    parser.add_argument(
        "--kb-name",
        type=str,
        nargs="+",
        default=[],
        help=(
            "specify knowledge base names to operate on. "
            "default is all folders exist in KB_ROOT_PATH."
        )
    )

    # 如果只输入了 python init_database.py 而没有其他参数，则打印帮助信息
    if len(sys.argv) <= 1:
        parser.print_help()
    else:
        # 解析命令行参数
        args = parser.parse_args()
        start_time = datetime.now()

        # 确保数据库表已经创建，如果不存在就自动创建
        create_tables()

        # 根据传入的不同参数，执行相应的操作
        if args.recreate_vs:
            # 重置数据库（清空表），然后对所有文档重新进行向量化
            reset_tables()
            print("database tables reseted")
            print("recreating all vector stores")
            folder2db(kb_names=args.kb_name, mode="recreate_vs")

        elif args.update_in_db:
            # 为数据库中已经存在的文档更新向量
            folder2db(kb_names=args.kb_name, mode="update_in_db")

        elif args.increament:
            # 只为本地新增加、尚未在数据库中的文档创建向量
            folder2db(kb_names=args.kb_name, mode="increament")

        elif args.prune_db:
            # 从数据库中删除不存在于本地文件夹的文档记录
            prune_db_docs(args.kb_name)

        elif args.prune_folder:
            # 从本地文件夹删除在数据库中已经不存在的文档文件
            prune_folder_files(args.kb_name)

        end_time = datetime.now()
        print(f"总计用时： {end_time - start_time}")