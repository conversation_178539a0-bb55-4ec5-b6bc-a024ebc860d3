# 政务行业助手系统数据库表结构设计

## 1. knowledge_base表（知识库表）

知识库表主要存储系统中各个知识库的基本信息。包括知识库ID、知识库名称、知识库简介、向量库类型、嵌入模型名称、文件数量和创建时间。知识库表结构如表1-1所示。

### 表1-1 知识库表结构

| 序号 | 字段名 | 类型 | 属性 | 描述 |
|------|-------|------|------|------|
| 1 | id | Integer | 主键，自增 | 知识库ID |
| 2 | kb_name | String(50) | 非空 | 知识库名称 |
| 3 | kb_info | String(200) | 可空 | 知识库简介(用于Agent) |
| 4 | vs_type | String(50) | 非空 | 向量库类型 |
| 5 | embed_model | String(50) | 非空 | 嵌入模型名称 |
| 6 | file_count | Integer | 默认值为0 | 文件数量 |
| 7 | create_time | DateTime | 默认为当前时间 | 创建时间 |

## 2. knowledge_file表（知识文件表）

知识文件表主要存储上传到知识库中的文件信息。包括知识文件ID、文件名、文件扩展名、所属知识库名称、文档加载器名称、文本分割器名称、文件版本、文件修改时间、文件大小、是否自定义docs、切分文档数量和创建时间。知识文件表结构如表2-1所示。

### 表2-1 知识文件表结构

| 序号 | 字段名 | 类型 | 属性 | 描述 |
|------|-------|------|------|------|
| 1 | id | Integer | 主键，自增 | 知识文件ID |
| 2 | file_name | String(255) | 非空 | 文件名 |
| 3 | file_ext | String(10) | 非空 | 文件扩展名 |
| 4 | kb_name | String(50) | 非空，外键 | 所属知识库名称 |
| 5 | document_loader_name | String(50) | 非空 | 文档加载器名称 |
| 6 | text_splitter_name | String(50) | 非空 | 文本分割器名称 |
| 7 | file_version | Integer | 默认值为1 | 文件版本 |
| 8 | file_mtime | Float | 默认值为0.0 | 文件修改时间 |
| 9 | file_size | Integer | 默认值为0 | 文件大小(字节) |
| 10 | custom_docs | Boolean | 默认值为False | 是否自定义docs |
| 11 | docs_count | Integer | 默认值为0 | 切分文档数量 |
| 12 | create_time | DateTime | 默认为当前时间 | 创建时间 |

## 3. file_doc表（文件-向量库文档关联表）

文件-向量库文档关联表主要存储文件与向量库中文档片段之间的映射关系。包括ID、知识库名称、文件名称、向量库文档ID和元数据。文件-向量库文档关联表结构如表3-1所示。

### 表3-1 文件-向量库文档关联表结构

| 序号 | 字段名 | 类型 | 属性 | 描述 |
|------|-------|------|------|------|
| 1 | id | Integer | 主键，自增 | ID |
| 2 | kb_name | String(50) | 非空，外键 | 知识库名称 |
| 3 | file_name | String(255) | 非空，外键 | 文件名称 |
| 4 | doc_id | String(50) | 非空 | 向量库文档ID |
| 5 | meta_data | JSON | 默认为空对象 | 文档元数据 |
