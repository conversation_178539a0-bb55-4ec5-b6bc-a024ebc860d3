import shutup
shutup.please()

import asyncio
import multiprocessing as mp  # Python 内置的多进程库，提供多进程相关功能
import os
import subprocess  # 用于调用子进程执行命令
import sys
from multiprocessing import Process
from datetime import datetime
from pprint import pprint

try:
    import numexpr

    # numexpr 用来进行大规模数值计算，可以利用多核CPU提高性能
    n_cores = numexpr.utils.detect_number_of_cores()
    os.environ["NUMEXPR_MAX_THREADS"] = str(n_cores)
except:
    # 如果 numexpr 不可用就直接 pass
    pass

# 将项目目录上级路径添加到 sys.path，以便导入上层目录的模块
sys.path.append(os.path.dirname(os.path.dirname(__file__)))
from configs import (
    LOG_PATH,
    log_verbose,
    logger,
    LLM_MODEL,
    EMBEDDING_MODEL,
    TEXT_SPLITTER_NAME,
    FSCHAT_CONTROLLER,
    FSCHAT_OPENAI_API,
    FSCHAT_MODEL_WORKERS,
    API_SERVER,
    WEBUI_SERVER,
    HTTPX_DEFAULT_TIMEOUT,
)
from server.utils import (
    fschat_controller_address,
    fschat_model_worker_address,
    fschat_openai_api_address,
    set_httpx_config,
    get_httpx_client,
    get_model_worker_config,
    get_all_model_worker_configs,
    MakeFastAPIOffline,
    FastAPI,
    llm_device,
    embedding_device
)
import argparse
from typing import Tuple, List, Dict
from configs import VERSION


def create_controller_app(
        dispatch_method: str,
        log_level: str = "INFO",
) -> FastAPI:
    """
    创建并返回一个 FastChat 的 Controller 应用。

    参数：
    - dispatch_method (str): 调度方法，控制如何分配请求给不同的模型 worker
    - log_level (str): 日志级别，如 "INFO"、"ERROR" 等
    """
    import fastchat.constants
    fastchat.constants.LOGDIR = LOG_PATH  # FastChat 的默认日志目录设置为配置中的 LOG_PATH
    from fastchat.serve.controller import app, Controller, logger
    logger.setLevel(log_level)

    # 实例化 Controller，controller 用于维护整个集群中可用的模型 worker 列表等信息
    controller = Controller(dispatch_method)
    # 将 controller 实例注入到 fastchat.serve.controller 模块中，供后续使用
    sys.modules["fastchat.serve.controller"].controller = controller

    # 将 FastAPI 应用设置为离线模式，去除 /openapi.json 和 /docs 相关接口
    MakeFastAPIOffline(app)
    app.title = "FastChat Controller"
    app._controller = controller  # 将 controller 实例存放到 app 对象上，方便访问
    return app


def create_model_worker_app(log_level: str = "INFO", **kwargs) -> FastAPI:
    """
    创建并返回一个 FastChat 的模型 worker 应用。根据 kwargs 中的不同字段来决定使用何种 worker 类。

    参数示例（kwargs）：
    - host: 模型 worker 运行地址
    - port: 模型 worker 端口
    - model_names: [‘model_name’] 指定要加载的模型名称
    - controller_address: 该 worker 注册到的 controller 地址
    - worker_address: 该 worker 自身的地址

    对于 Langchain 支持的模型：
        langchain_model: True
        不会使用 fschat 自身模型的加载逻辑，而是使用 Langchain 方式
    对于在线 API 模型：
        online_api: True
        worker_class: 'provider'
    对于离线模型：
        model_path: 模型名称或路径，可能是 huggingface 的 repo-id 或本地路径
        device: 'LLM_DEVICE'，指定部署到 CPU 或 GPU
    """
    import fastchat.constants
    fastchat.constants.LOGDIR = LOG_PATH  # 设置 FastChat 的日志目录
    import argparse

    parser = argparse.ArgumentParser()
    args = parser.parse_args([])  # 不从命令行解析，手动创建空参

    # 将 kwargs 中所有的键值对存储到 args 中，以兼容 FastChat 原本的参数结构
    for k, v in kwargs.items():
        setattr(args, k, v)

    # 如果标记了 langchain_model，就说明是 Langchain 模型，不走下面的逻辑
    if worker_class := kwargs.get("langchain_model"):  # Langchian 支持的模型不用做操作
        from fastchat.serve.base_model_worker import app
        worker = ""
    # 如果标记了 worker_class，就说明是在线模型 API
    elif worker_class := kwargs.get("worker_class"):
        from fastchat.serve.base_model_worker import app

        # 这里直接用 worker_class 初始化
        worker = worker_class(
            model_names=args.model_names,
            controller_addr=args.controller_address,
            worker_addr=args.worker_address
        )
        # 将日志级别设置为传入的 log_level
        sys.modules["fastchat.serve.base_model_worker"].logger.setLevel(log_level)
    # 否则就是离线模型
    else:
        from configs.model_config import VLLM_MODEL_DICT
        if kwargs["model_names"][0] in VLLM_MODEL_DICT and args.infer_turbo == "vllm":
            """
            如果模型名称在 VLLM_MODEL_DICT 里，且 infer_turbo 配置为 vllm，
            则使用 vllm_worker 来启动 vllm 的推理引擎。
            """
            import fastchat.serve.vllm_worker
            from fastchat.serve.vllm_worker import VLLMWorker, app
            from vllm import AsyncLLMEngine
            from vllm.engine.arg_utils import AsyncEngineArgs, EngineArgs

            # 针对 vllm 的参数进行初始化
            args.tokenizer = args.model_path
            args.tokenizer_mode = 'auto'
            args.trust_remote_code = True
            args.download_dir = None
            args.load_format = 'auto'
            args.dtype = 'auto'
            args.seed = 0
            args.worker_use_ray = False
            args.pipeline_parallel_size = 1
            args.tensor_parallel_size = 1
            args.block_size = 16
            args.swap_space = 4  # 单位 GiB
            args.gpu_memory_utilization = 0.90
            args.max_num_batched_tokens = 16384  # 一个批次中的最大 tokens 数量
            args.max_num_seqs = 256
            args.disable_log_stats = False
            args.conv_template = None
            args.limit_worker_concurrency = 5
            args.no_register = False
            args.num_gpus = 1  # vllm worker 的 tensor 并行GPU数
            args.engine_use_ray = False
            args.disable_log_requests = False

            # 以下为 vllm 0.2.0 版本后新增可能需要的参数，这里暂不启用
            args.max_model_len = None
            args.revision = None
            args.quantization = None
            args.max_log_len = None

            # 如果指定了 model_path，则将其赋值给 args.model
            if args.model_path:
                args.model = args.model_path
            # 如果指定了多 GPU
            if args.num_gpus > 1:
                args.tensor_parallel_size = args.num_gpus

            # 将上面的更新再次写入 args
            for k, v in kwargs.items():
                setattr(args, k, v)

            # 根据命令行参数生成异步引擎的参数
            engine_args = AsyncEngineArgs.from_cli_args(args)
            # 初始化异步引擎
            engine = AsyncLLMEngine.from_engine_args(engine_args)

            # 创建 vllm 的 worker
            worker = VLLMWorker(
                controller_addr=args.controller_address,
                worker_addr=args.worker_address,
                worker_id=worker_id,
                model_path=args.model_path,
                model_names=args.model_names,
                limit_worker_concurrency=args.limit_worker_concurrency,
                no_register=args.no_register,
                llm_engine=engine,
                conv_template=args.conv_template,
            )
            # 将 engine 和 worker 注册到 sys.modules 中
            sys.modules["fastchat.serve.vllm_worker"].engine = engine
            sys.modules["fastchat.serve.vllm_worker"].logger.setLevel(log_level)

        else:
            """
            如果不是 vllm 模式，那么就走 model_worker 的传统本地模型加载流程。
            """
            from fastchat.serve.model_worker import app, GptqConfig, AWQConfig, ModelWorker, worker_id

            # 一些默认参数的设置
            args.gpus = "0"  # 如果有多个 GPU 可以指定 "0,1,2"
            args.max_gpu_memory = "22GiB"
            args.num_gpus = 1

            args.load_8bit = False
            args.cpu_offloading = None
            args.gptq_ckpt = None
            args.gptq_wbits = 16
            args.gptq_groupsize = -1
            args.gptq_act_order = False
            args.awq_ckpt = None
            args.awq_wbits = 16
            args.awq_groupsize = -1
            args.model_names = [""]
            args.conv_template = None
            args.limit_worker_concurrency = 5
            args.stream_interval = 2
            args.no_register = False
            args.embed_in_truncate = False

            for k, v in kwargs.items():
                setattr(args, k, v)

            # 如果有 GPU 信息
            if args.gpus:
                if args.num_gpus is None:
                    args.num_gpus = len(args.gpus.split(','))
                if len(args.gpus.split(",")) < args.num_gpus:
                    raise ValueError(
                        f"Larger --num-gpus ({args.num_gpus}) than --gpus {args.gpus}!"
                    )
                # 设置环境变量，指定当前可用的 GPU
                os.environ["CUDA_VISIBLE_DEVICES"] = args.gpus

            # GptqConfig / AWQConfig 配置量化相关信息
            gptq_config = GptqConfig(
                ckpt=args.gptq_ckpt or args.model_path,
                wbits=args.gptq_wbits,
                groupsize=args.gptq_groupsize,
                act_order=args.gptq_act_order,
            )
            awq_config = AWQConfig(
                ckpt=args.awq_ckpt or args.model_path,
                wbits=args.awq_wbits,
                groupsize=args.awq_groupsize,
            )

            # 初始化一个 ModelWorker 对象
            worker = ModelWorker(
                controller_addr=args.controller_address,
                worker_addr=args.worker_address,
                worker_id=worker_id,
                model_path=args.model_path,
                model_names=args.model_names,
                limit_worker_concurrency=args.limit_worker_concurrency,
                no_register=args.no_register,
                device=args.device,
                num_gpus=args.num_gpus,
                max_gpu_memory=args.max_gpu_memory,
                load_8bit=args.load_8bit,
                cpu_offloading=args.cpu_offloading,
                gptq_config=gptq_config,
                awq_config=awq_config,
                stream_interval=args.stream_interval,
                conv_template=args.conv_template,
                embed_in_truncate=args.embed_in_truncate,
            )
            # 将参数与日志注册到对应的模块下，以便在其他地方访问
            sys.modules["fastchat.serve.model_worker"].args = args
            sys.modules["fastchat.serve.model_worker"].gptq_config = gptq_config
            sys.modules["fastchat.serve.model_worker"].logger.setLevel(log_level)

    # 将应用设置为离线模式
    MakeFastAPIOffline(app)
    # 标题中显示第一个模型的名称
    app.title = f"FastChat LLM Server ({args.model_names[0]})"
    # 将 worker 实例保存到 app 上
    app._worker = worker
    return app


def create_openai_api_app(
        controller_address: str,
        api_keys: List = [],
        log_level: str = "INFO",
) -> FastAPI:
    """
    创建并返回一个 FastChat 的 openai_api server，模拟 OpenAI 的接口形式。

    参数：
    - controller_address (str): 控制器的地址，用于路由请求
    - api_keys (List): 用户的 API key 列表
    - log_level (str): 日志级别
    """
    import fastchat.constants
    fastchat.constants.LOGDIR = LOG_PATH
    from fastchat.serve.openai_api_server import app, CORSMiddleware, app_settings
    from fastchat.utils import build_logger
    logger = build_logger("openai_api", "openai_api.log")
    logger.setLevel(log_level)

    # 添加跨域中间件，允许所有跨域
    app.add_middleware(
        CORSMiddleware,
        allow_credentials=True,
        allow_origins=["*"],
        allow_methods=["*"],
        allow_headers=["*"],
    )

    # 注册日志与相关设置
    sys.modules["fastchat.serve.openai_api_server"].logger = logger
    app_settings.controller_address = controller_address
    app_settings.api_keys = api_keys

    # 去除 /openapi.json 和 /docs 等内容
    MakeFastAPIOffline(app)
    app.title = "FastChat OpeanAI API Server"
    return app


def _set_app_event(app: FastAPI, started_event: mp.Event = None):
    """
    给 app 注册一个启动事件的回调，用于在 FastAPI 启动完成时通知主进程。
    """

    @app.on_event("startup")
    async def on_startup():
        if started_event is not None:
            started_event.set()


def run_controller(log_level: str = "INFO", started_event: mp.Event = None):
    """
    启动 controller 服务的函数。它会启动一个 uvicorn 服务来运行 controller 应用。
    """
    import uvicorn
    import httpx
    from fastapi import Body
    import time
    import sys
    from server.utils import set_httpx_config
    set_httpx_config()

    # 创建一个 controller FastAPI 应用实例
    app = create_controller_app(
        dispatch_method=FSCHAT_CONTROLLER.get("dispatch_method"),
        log_level=log_level,
    )
    _set_app_event(app, started_event)

    # 新增一个接口，用于释放和切换模型 worker
    @app.post("/release_worker")
    def release_worker(
            model_name: str = Body(..., description="要释放模型的名称", samples=["chatglm-6b"]),
            # worker_address: str = Body(None, description="要释放模型的地址，与名称二选一", samples=[FSCHAT_CONTROLLER_address()]),
            new_model_name: str = Body(None, description="释放后加载该模型"),
            keep_origin: bool = Body(False, description="不释放原模型，加载新模型")
    ) -> Dict:
        """
        释放指定名称的模型，如果需要可以加载新的模型。
        """
        available_models = app._controller.list_models()  # 获取当前可用的模型列表
        if new_model_name in available_models:
            msg = f"要切换的LLM模型 {new_model_name} 已经存在"
            logger.info(msg)
            return {"code": 500, "msg": msg}

        if new_model_name:
            logger.info(f"开始切换LLM模型：从 {model_name} 到 {new_model_name}")
        else:
            logger.info(f"即将停止LLM模型： {model_name}")

        # 检查 model_name 是否存在
        if model_name not in available_models:
            msg = f"the model {model_name} is not available"
            logger.error(msg)
            return {"code": 500, "msg": msg}

        # 获取对应的 worker 地址
        worker_address = app._controller.get_worker_address(model_name)
        if not worker_address:
            msg = f"can not find model_worker address for {model_name}"
            logger.error(msg)
            return {"code": 500, "msg": msg}

        # 调用对应 worker 的 /release 接口请求释放模型
        with get_httpx_client() as client:
            r = client.post(worker_address + "/release",
                            json={"new_model_name": new_model_name, "keep_origin": keep_origin})
            if r.status_code != 200:
                msg = f"failed to release model: {model_name}"
                logger.error(msg)
                return {"code": 500, "msg": msg}

        # 如果释放后需要加载新模型，需要等待 new_model_name 的 worker 注册到 controller
        if new_model_name:
            timer = HTTPX_DEFAULT_TIMEOUT  # 等待 new_model_name 注册的超时时间
            while timer > 0:
                models = app._controller.list_models()
                if new_model_name in models:
                    break
                time.sleep(1)
                timer -= 1
            if timer > 0:
                msg = f"sucess change model from {model_name} to {new_model_name}"
                logger.info(msg)
                return {"code": 200, "msg": msg}
            else:
                msg = f"failed change model from {model_name} to {new_model_name}"
                logger.error(msg)
                return {"code": 500, "msg": msg}
        else:
            # 仅释放模型，不切换到其他模型
            msg = f"sucess to release model: {model_name}"
            logger.info(msg)
            return {"code": 200, "msg": msg}

    # 从配置中获取 controller 的 host 和 port
    host = FSCHAT_CONTROLLER["host"]
    port = FSCHAT_CONTROLLER["port"]

    # 如果日志级别是 ERROR，就直接把输出重定向到原始 stdout/stderr，减少日志输出
    if log_level == "ERROR":
        sys.stdout = sys.__stdout__
        sys.stderr = sys.__stderr__

    # 启动 uvicorn 服务
    uvicorn.run(app, host=host, port=port, log_level=log_level.lower())


def run_model_worker(
        model_name: str = LLM_MODEL,
        controller_address: str = "",
        log_level: str = "INFO",
        q: mp.Queue = None,
        started_event: mp.Event = None,
):
    """
    启动模型 worker 的函数，启动一个 uvicorn 服务来运行 model_worker 应用。
    """
    import uvicorn
    from fastapi import Body
    import sys
    from server.utils import set_httpx_config
    set_httpx_config()

    # 通过 model_name 找到对应的配置信息
    kwargs = get_model_worker_config(model_name)
    host = kwargs.pop("host")
    port = kwargs.pop("port")
    kwargs["model_names"] = [model_name]
    # 如果没有传入 controller_address 就使用默认的
    kwargs["controller_address"] = controller_address or fschat_controller_address()
    kwargs["worker_address"] = fschat_model_worker_address(model_name)
    model_path = kwargs.get("model_path", "")
    kwargs["model_path"] = model_path

    # 创建 worker 的 FastAPI 应用实例
    app = create_model_worker_app(log_level=log_level, **kwargs)
    _set_app_event(app, started_event)

    # 如果日志级别是 ERROR，就减少日志输出
    if log_level == "ERROR":
        sys.stdout = sys.__stdout__
        sys.stderr = sys.__stderr__

    # 添加一个 /release 接口，用于释放当前模型，并可根据需要加载新模型
    @app.post("/release")
    def release_model(
            new_model_name: str = Body(None, description="释放后加载该模型"),
            keep_origin: bool = Body(False, description="不释放原模型，加载新模型")
    ) -> Dict:
        if keep_origin:
            # 不释放原模型，直接启动一个新模型
            if new_model_name:
                q.put([model_name, "start", new_model_name])
        else:
            # 如果有新模型，需要替换；否则就停止当前模型
            if new_model_name:
                q.put([model_name, "replace", new_model_name])
            else:
                q.put([model_name, "stop", None])
        return {"code": 200, "msg": "done"}

    # 启动 uvicorn
    uvicorn.run(app, host=host, port=port, log_level=log_level.lower())


def run_openai_api(log_level: str = "INFO", started_event: mp.Event = None):
    """
    启动 openai_api 服务函数，uvicorn 方式
    """
    import uvicorn
    import sys
    from server.utils import set_httpx_config
    set_httpx_config()

    # 获取 controller 地址，创建 openai_api_app
    controller_addr = fschat_controller_address()
    app = create_openai_api_app(controller_addr, log_level=log_level)  # 目前不支持 keys
    _set_app_event(app, started_event)

    # 从配置中获取 openai api 的 host 和 port
    host = FSCHAT_OPENAI_API["host"]
    port = FSCHAT_OPENAI_API["port"]

    if log_level == "ERROR":
        sys.stdout = sys.__stdout__
        sys.stderr = sys.__stderr__
    uvicorn.run(app, host=host, port=port)


def run_api_server(started_event: mp.Event = None):
    """
    启动后端 API 服务（server/api.py 中定义的 FastAPI 应用），用于普通接口调用。
    """
    from server.api import create_app
    import uvicorn
    from server.utils import set_httpx_config
    set_httpx_config()

    app = create_app()
    _set_app_event(app, started_event)

    host = API_SERVER["host"]
    port = API_SERVER["port"]

    uvicorn.run(app, host=host, port=port)


def run_webui(started_event: mp.Event = None):
    """
    启动 WebUI（基于 Streamlit），用于在浏览器中可视化交互。
    """
    from server.utils import set_httpx_config
    set_httpx_config()

    host = WEBUI_SERVER["host"]
    port = WEBUI_SERVER["port"]

    # 启动子进程调用 streamlit 命令
    p = subprocess.Popen([
        "streamlit",
        "run",
        "webui.py",
        "--server.address", host,
        "--server.port", str(port),
        "--theme.base", "light",
        "--theme.primaryColor", "#165dff",
        "--theme.secondaryBackgroundColor", "#e0e0e0",
        "--theme.textColor", "#000000",
    ])
    started_event.set()
    p.wait()


def parse_args() -> argparse.ArgumentParser:
    """
    解析命令行参数。可以通过命令行选项指定要启动哪些服务。
    """
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "-a",
        "--all-webui",
        action="store_true",
        help="run fastchat's controller/openai_api/model_worker servers, run api.py and webui.py",
        dest="all_webui",
    )
    parser.add_argument(
        "--all-api",
        action="store_true",
        help="run fastchat's controller/openai_api/model_worker servers, run api.py",
        dest="all_api",
    )
    parser.add_argument(
        "--llm-api",
        action="store_true",
        help="run fastchat's controller/openai_api/model_worker servers",
        dest="llm_api",
    )
    parser.add_argument(
        "-o",
        "--openai-api",
        action="store_true",
        help="run fastchat's controller/openai_api servers",
        dest="openai_api",
    )
    parser.add_argument(
        "-m",
        "--model-worker",
        action="store_true",
        help="run fastchat's model_worker server with specified model name. specify --model-name if not using default LLM_MODEL",
        dest="model_worker",
    )
    parser.add_argument(
        "-n",
        "--model-name",
        type=str,
        nargs="+",
        default=[LLM_MODEL],
        help="specify model name for model worker. add addition names with space seperated to start multiple model workers.",
        dest="model_name",
    )
    parser.add_argument(
        "-c",
        "--controller",
        type=str,
        help="specify controller address the worker is registered to. default is FSCHAT_CONTROLLER",
        dest="controller_address",
    )
    parser.add_argument(
        "--api",
        action="store_true",
        help="run api.py server",
        dest="api",
    )
    parser.add_argument(
        "-p",
        "--api-worker",
        action="store_true",
        help="run online model api such as zhipuai",
        dest="api_worker",
    )
    parser.add_argument(
        "-w",
        "--webui",
        action="store_true",
        help="run webui.py server",
        dest="webui",
    )
    parser.add_argument(
        "-q",
        "--quiet",
        action="store_true",
        help="减少fastchat服务log信息",
        dest="quiet",
    )
    args = parser.parse_args()
    return args, parser


def dump_server_info(after_start=False, args=None):
    """
    打印一些服务器相关的信息，如操作系统、python、langchain、fastchat等版本信息，以及当前使用的模型名称等。
    """
    import platform
    import langchain
    import fastchat
    from server.utils import api_address, webui_address

    print("\n")
    print(f"操作系统：{platform.platform()}.")
    print(f"python版本：{sys.version}")
    print(f"langchain版本：{langchain.__version__}. fastchat版本：{fastchat.__version__}")
    print("\n")

    models = [LLM_MODEL]
    if args and args.model_name:
        models = args.model_name

    print(f"当前使用的分词器：{TEXT_SPLITTER_NAME}")
    print(f"当前启动的LLM模型：{models} @ {llm_device()}")

    for model in models:
        pprint(get_model_worker_config(model))
    print(f"当前Embbedings模型： {EMBEDDING_MODEL} @ {embedding_device()}")

    if after_start:
        print("\n")
        print(f"服务端运行信息：")
        if args.webui:
            print(f"    Chatchat WEBUI Server: {webui_address()}")
    print("\n")


# 异步方式启动主服务
async def start_main_server():
    """
    此函数是启动所有相关子进程/服务的主要入口，包括：
    - controller
    - openai_api
    - model_worker
    - api_server
    - webui
    并根据命令行参数来决定启动哪些服务。
    """
    import time
    import signal

    def handler(signalname):
        def f(signal_received, frame):
            raise KeyboardInterrupt(f"{signalname} received")

        return f

    # 捕捉 SIGINT 和 SIGTERM 信号，将其转换为 KeyboardInterrupt
    signal.signal(signal.SIGINT, handler("SIGINT"))
    signal.signal(signal.SIGTERM, handler("SIGTERM"))

    # 设置多进程启动方式为 "spawn"，兼容性更好
    mp.set_start_method("spawn")
    manager = mp.Manager()

    # 进程间通信的队列，用于模型热切换时传递指令
    queue = manager.Queue()
    args, parser = parse_args()

    # 处理一些组合参数，一次性启动多个服务
    if args.all_webui:
        # 同时启动 controller/openai_api/model_worker + api + webui
        args.openai_api = True
        args.model_worker = True
        args.api = True
        args.api_worker = True
        args.webui = True
    elif args.all_api:
        # 同时启动 controller/openai_api/model_worker + api
        args.openai_api = True
        args.model_worker = True
        args.api = True
        args.api_worker = True
        args.webui = False
    elif args.llm_api:
        # 同时启动 controller/openai_api/model_worker
        args.openai_api = True
        args.model_worker = True
        args.api_worker = True
        args.api = False
        args.webui = False

    # 打印当前配置的服务器信息
    dump_server_info(args=args)

    if len(sys.argv) > 1:
        logger.info(f"正在启动服务：")
        logger.info(f"如需查看 llm_api 日志，请前往 {LOG_PATH}")

    # 使用一个字典来存放所有进程
    processes = {"online_api": {}, "model_worker": {}}

    def process_count():
        # 计算当前需要启动的进程总数（dict 里含有嵌套 dict，需要做一些处理）
        return len(processes) + len(processes["online_api"]) + len(processes["model_worker"]) - 2

    # 根据 quiet 或项目配置，决定日志级别
    if args.quiet or not log_verbose:
        log_level = "ERROR"
    else:
        log_level = "INFO"

    # controller 启动事件
    controller_started = manager.Event()
    # 如果需要启动 openai_api
    if args.openai_api:
        # 启动 controller
        process = Process(
            target=run_controller,
            name=f"controller",
            kwargs=dict(log_level=log_level, started_event=controller_started),
            daemon=True,
        )
        processes["controller"] = process

        # 启动 openai_api
        process = Process(
            target=run_openai_api,
            name=f"openai_api",
            daemon=True,
        )
        processes["openai_api"] = process

    # 记录多个 model_worker 的启动事件，用于等待
    model_worker_started = []
    if args.model_worker:
        # 对于需要启动的模型列表逐一启动一个 model_worker 进程
        for model_name in args.model_name:
            config = get_model_worker_config(model_name)
            # 如果是在线API配置，不在这里处理
            if not config.get("online_api"):
                e = manager.Event()
                model_worker_started.append(e)
                process = Process(
                    target=run_model_worker,
                    name=f"model_worker - {model_name}",
                    kwargs=dict(
                        model_name=model_name,
                        controller_address=args.controller_address,
                        log_level=log_level,
                        q=queue,
                        started_event=e
                    ),
                    daemon=True,
                )
                processes["model_worker"][model_name] = process

    # 如果需要启动在线API对应的模型 worker（例如智谱API）
    if args.api_worker:
        configs = get_all_model_worker_configs()
        # 遍历所有 worker 配置，找到 online_api = True 且在 FSCHAT_MODEL_WORKERS 中的，启动该进程
        for model_name, config in configs.items():
            if (config.get("online_api")
                    and config.get("worker_class")
                    and model_name in FSCHAT_MODEL_WORKERS):
                e = manager.Event()
                model_worker_started.append(e)
                process = Process(
                    target=run_model_worker,
                    name=f"api_worker - {model_name}",
                    kwargs=dict(
                        model_name=model_name,
                        controller_address=args.controller_address,
                        log_level=log_level,
                        q=queue,
                        started_event=e
                    ),
                    daemon=True,
                )
                processes["online_api"][model_name] = process

    # API 服务启动事件
    api_started = manager.Event()
    # 如果需要启动 api.py 服务
    if args.api:
        process = Process(
            target=run_api_server,
            name=f"API Server",
            kwargs=dict(started_event=api_started),
            daemon=True,
        )
        processes["api"] = process

    # WebUI 启动事件
    webui_started = manager.Event()
    # 如果需要启动 webui
    if args.webui:
        process = Process(
            target=run_webui,
            name=f"WEBUI Server",
            kwargs=dict(started_event=webui_started),
            daemon=True,
        )
        processes["webui"] = process

    # 如果最终一个进程都没配需要启动，就打印帮助信息
    if process_count() == 0:
        parser.print_help()
    else:
        try:
            # 启动 controller
            if p := processes.get("controller"):
                p.start()
                p.name = f"{p.name} ({p.pid})"
                controller_started.wait()  # 等待 controller 启动完成

            # 启动 openai_api
            if p := processes.get("openai_api"):
                p.start()
                p.name = f"{p.name} ({p.pid})"

            # 启动所有本地模型 worker
            for n, p in processes.get("model_worker", {}).items():
                p.start()
                p.name = f"{p.name} ({p.pid})"

            # 启动所有在线API worker
            for n, p in processes.get("online_api", []).items():
                p.start()
                p.name = f"{p.name} ({p.pid})"

            # 等待所有 model_worker 启动完成
            for e in model_worker_started:
                e.wait()

            # 启动 api 服务
            if p := processes.get("api"):
                p.start()
                p.name = f"{p.name} ({p.pid})"
                api_started.wait()

            # 启动 webui
            if p := processes.get("webui"):
                p.start()
                p.name = f"{p.name} ({p.pid})"
                webui_started.wait()

            # 再次打印服务信息，提示已经启动成功
            dump_server_info(after_start=True, args=args)

            # 主循环，监听队列中的模型切换指令
            while True:
                cmd = queue.get()  # 获取队列中的指令
                e = manager.Event()
                if isinstance(cmd, list):
                    model_name, cmd, new_model_name = cmd
                    if cmd == "start":
                        # 启动一个新的模型进程，但不释放旧模型
                        logger.info(f"准备启动新模型进程：{new_model_name}")
                        process = Process(
                            target=run_model_worker,
                            name=f"model_worker - {new_model_name}",
                            kwargs=dict(
                                model_name=new_model_name,
                                controller_address=args.controller_address,
                                log_level=log_level,
                                q=queue,
                                started_event=e
                            ),
                            daemon=True,
                        )
                        process.start()
                        process.name = f"{process.name} ({process.pid})"
                        processes["model_worker"][new_model_name] = process
                        e.wait()
                        logger.info(f"成功启动新模型进程：{new_model_name}")
                    elif cmd == "stop":
                        # 停止当前模型进程
                        if process := processes["model_worker"].get(model_name):
                            time.sleep(1)
                            process.terminate()
                            process.join()
                            logger.info(f"停止模型进程：{model_name}")
                        else:
                            logger.error(f"未找到模型进程：{model_name}")
                    elif cmd == "replace":
                        # 停止旧模型，启动新模型
                        if process := processes["model_worker"].pop(model_name, None):
                            logger.info(f"停止模型进程：{model_name}")
                            start_time = datetime.now()
                            time.sleep(1)
                            process.terminate()
                            process.join()
                            process = Process(
                                target=run_model_worker,
                                name=f"model_worker - {new_model_name}",
                                kwargs=dict(
                                    model_name=new_model_name,
                                    controller_address=args.controller_address,
                                    log_level=log_level,
                                    q=queue,
                                    started_event=e
                                ),
                                daemon=True,
                            )
                            process.start()
                            process.name = f"{process.name} ({process.pid})"
                            processes["model_worker"][new_model_name] = process
                            e.wait()
                            timing = datetime.now() - start_time
                            logger.info(f"成功启动新模型进程：{new_model_name}。用时：{timing}。")
                        else:
                            logger.error(f"未找到模型进程：{model_name}")

        except Exception as e:
            logger.error(e)
            logger.warning("Caught KeyboardInterrupt! Setting stop event...")
        finally:
            # 主进程结束时，杀掉所有子进程
            for p in processes.values():
                logger.warning("Sending SIGKILL to %s", p)
                if isinstance(p, dict):
                    for process in p.values():
                        process.kill()
                else:
                    p.kill()

            for p in processes.values():
                logger.info("Process status: %s", p)


if __name__ == "__main__":
    # python 3.10 后可以使用 get_running_loop()
    if sys.version_info < (3, 10):
        loop = asyncio.get_event_loop()
    else:
        try:
            loop = asyncio.get_running_loop()
        except RuntimeError:
            loop = asyncio.new_event_loop()

        asyncio.set_event_loop(loop)
    loop.run_until_complete(start_main_server())