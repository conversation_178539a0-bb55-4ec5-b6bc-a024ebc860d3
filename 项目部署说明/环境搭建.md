# 环境搭建

## 1 安装C++编译工具

在安装 **Microsoft Visual C++ Build Tools** 时，需要选择以下具体组件，确保能够编译 `xformers`：

1. **MSVC 工具集**（Microsoft C++ 编译工具）
   - 具体名称：`MSVC v143 - VS 2022 C++ x64/x86 Build Tools` 或较新版本。
   - 功能：提供 C++ 编译器（`cl.exe`）和相关工具。

2. **Windows SDK**
   - 具体名称：
     - Windows 10 SDK (10.0.xxxxx.x)
     - 或 Windows 11 SDK（根据你的系统版本选择最新版本）
   - 功能：提供 Windows 平台的必要开发头文件和库。

在安装结束后，还要将cl.exe所在bin添加到系统环境变量path中。

## 2. 安装环境依赖

1. 先装torch==2.0.1
```
pip install torch==2.0.1 torchvision==0.15.2 torchaudio==2.0.2
```
<!-- pip install torch==2.1.0 torchvision==0.16.0 torchaudio==2.1.0 --index-url https://download.pytorch.org/whl/cu118 -->

2. 再强制根据requirements.txt安装

注意，这里要使用xformers==0.0.22->对应torch==2.0.1

可以参考这篇博客：[xformers版本与其依赖pytorch版本的对应关系](https://blog.csdn.net/BigerBang/article/details/140301608)

```
pip install --force-reinstall -r requirements.txt
```

## 3. 下载必要模型

[国内用户 HuggingFace 高速下载](https://github.com/LetheSec/HuggingFace-Download-Accelerator/tree/main)

下载模型命令
1. embed_model
```
python hf_download.py --model moka-ai/m3e-base --save_dir P:\Workspace\Python\zhengwuhangyezhushou3\model
```
在`configs/model_config.py`中的`MODEL_PATH`替换路径

2. llm_model：待研究，暂时使用线上模型

如果环境弄坏了，可以使用下述指令卸载所有包

```
pip freeze | xargs pi
```
其他未在requirements.txt提到的包也需要安装下：
   - jieba 
   - rank_bm25

```
pip install jieba rank_bm25
```

至此，环境已搭建完成。

---

## 对CSV文档进行嵌入并写入向量数据库

1. 首先将`P:\Workspace\Python\zhengwuhangyezhushou3\server\knowledge_base\kb_cache\faiss_cache.py`文件中的`FAISS.load_local()`函数添加参数`allow_dangerous_deserialization=True`，以防止触发“危险反序列化”保护的报错。
2. 在终端执行`python init_database.py --recreate-vs`，对CSV文档进行嵌入并写入向量数据库。
3. 具体内容参考以下文档：[对CSV文档进行嵌入并写入向量数据库的终端输出说明](对CSV文档进行嵌入并写入向量数据库的终端输出说明.md)



## 前端

```
streamlit run P:\Workspace\Python\zhengwuhangyezhushou3\webui.py
```

## 后端

```
python startup.py -a

```