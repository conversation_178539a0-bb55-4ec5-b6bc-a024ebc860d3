"""
该模块实现了一个用于文本分割的类AliTextSplitter。该类继承自CharacterTextSplitter，
并提供了一种基于正则表达式和达摩院开源的nlp_bert_document-segmentation_chinese-base模型的文本分割方法。
可以根据需要选择是否处理PDF格式的文本。
"""

from langchain.text_splitter import CharacterTextSplitter  # 从langchain.text_splitter导入CharacterTextSplitter类
import re  # 导入正则表达式模块
from typing import List  # 导入List类型用于类型注解

class AliTextSplitter(CharacterTextSplitter):
    """用于文本分割的类，继承自CharacterTextSplitter。

    Attributes:
        pdf (bool): 指示是否处理PDF格式的文本。
    """

    def __init__(self, pdf: bool = False, **kwargs):
        """初始化AliTextSplitter类的实例。

        Args:
            pdf (bool): 是否处理PDF格式的文本，默认为False。
            **kwargs: 其他可选参数。
        """
        super().__init__(**kwargs)  # 调用父类的初始化方法
        self.pdf = pdf  # 设置pdf属性

    def split_text(self, text: str) -> List[str]:
        """使用正则表达式和模型分割文本为句子列表。

        Args:
            text (str): 要分割的输入文本。

        Returns:
            List[str]: 分割后的句子列表。
        """
        # use_document_segmentation参数指定是否用语义切分文档，此处采取的文档语义分割模型为达摩院开源的nlp_bert_document-segmentation_chinese-base，论文见https://arxiv.org/abs/2107.09278
        # 如果使用模型进行文档语义切分，那么需要安装modelscope[nlp]：pip install "modelscope[nlp]" -f https://modelscope.oss-cn-beijing.aliyuncs.com/releases/repo.html
        # 考虑到使用了三个模型，可能对于低配置gpu不太友好，因此这里将模型load进cpu计算，有需要的话可以替换device为自己的显卡id
        if self.pdf:  # 如果处理PDF格式的文本
            text = re.sub(r"\n{3,}", r"\n", text)  # 替换连续三个及以上的换行符为单个换行符
            text = re.sub('\s', " ", text)  # 将所有空白字符替换为空格
            text = re.sub("\n\n", "", text)  # 移除双换行符
        try:
            from modelscope.pipelines import pipeline  # 尝试导入modelscope的pipeline模块
        except ImportError:
            raise ImportError(
                "Could not import modelscope python package. "
                "Please install modelscope with `pip install modelscope`. "
            )  # 如果导入失败，抛出导入错误并提示安装modelscope

        # 创建一个文档分割的pipeline，使用达摩院的nlp_bert_document-segmentation_chinese-base模型
        p = pipeline(
            task="document-segmentation",  # 指定任务为文档分割
            model='damo/nlp_bert_document-segmentation_chinese-base',  # 指定使用的模型
            device="cpu")  # 指定设备为cpu
        result = p(documents=text)  # 使用pipeline对文本进行分割
        sent_list = [i for i in result["text"].split("\n\t") if i]  # 将结果按换行符分割为句子列表，并移除空元素
        return sent_list  # 返回分割后的句子列表
