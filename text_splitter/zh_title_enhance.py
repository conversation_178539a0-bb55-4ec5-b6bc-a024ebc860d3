"""
该模块用于增强中文标题的识别和处理。
通过一系列的规则和检查，判断文本是否可能是标题，并在metadata中标记
然后将文本与上一级标题进行拼合，实现标题增强

具体介绍
https://agijuejin.feishu.cn/wiki/NcwswcVIxicwCJk6scFcuQqznqc
"""

from langchain.docstore.document import Document  # 导入Document类，用于文档处理
import re  # 导入正则表达式模块，用于文本模式匹配

def under_non_alpha_ratio(text: str, threshold: float = 0.5):
    """检查文本中非字母字符的比例是否超过给定的阈值。
    
    这有助于防止像"-----------BREAK---------"这样的文本被标记为标题或叙述文本。比例不计算空格。

    参数
    ----------
    text
        要测试的输入字符串
    threshold
        如果非字母字符的比例超过此阈值，函数返回False
    """
    if len(text) == 0:  # 如果文本长度为0，直接返回False
        return False

    alpha_count = len([char for char in text if char.strip() and char.isalpha()])  # 计算字母字符的数量
    total_count = len([char for char in text if char.strip()])  # 计算非空字符的总数
    try:
        ratio = alpha_count / total_count  # 计算字母字符占总字符的比例
        return ratio < threshold  # 如果比例小于阈值，返回True，否则返回False
    except:
        return False  # 如果出现异常，返回False

def is_possible_title(
        text: str,
        title_max_word_length: int = 20,
        non_alpha_threshold: float = 0.5,
) -> bool:
    """检查文本是否通过所有有效标题的检查。

    参数
    ----------
    text
        要检查的输入文本
    title_max_word_length
        标题可以包含的最大单词数
    non_alpha_threshold
        文本需要被视为标题的最小字母字符数
    """

    # 文本长度为0的话，肯定不是title
    if len(text) == 0:
        print("Not a title. Text is empty.")  # 打印信息，文本为空
        return False

    # 文本中有标点符号，就不是title
    ENDS_IN_PUNCT_PATTERN = r"[^\w\s]\Z"  # 定义正则表达式模式，匹配以标点符号结尾的文本
    ENDS_IN_PUNCT_RE = re.compile(ENDS_IN_PUNCT_PATTERN)  # 编译正则表达式
    if ENDS_IN_PUNCT_RE.search(text) is not None:  # 如果文本中有标点符号，返回False
        return False

    # 文本长度不能超过设定值，默认20
    # NOTE: 这里使用空格分割而不是词语标记化，因为它更省资源，且实际标记化对长度检查没有太大价值
    if len(text) > title_max_word_length:  # 如果文本长度超过最大单词数，返回False
        return False

    # 文本中数字的占比不能太高，否则不是title
    if under_non_alpha_ratio(text, threshold=non_alpha_threshold):  # 如果非字母字符比例超过阈值，返回False
        return False

    # NOTE: 防止将像"To My Dearest Friends,"这样的称呼标记为标题
    if text.endswith((",", ".", "，", "。")):  # 如果文本以逗号或句号结尾，返回False
        return False

    if text.isnumeric():  # 如果文本全为数字，返回False
        print(f"Not a title. Text is all numeric:\n\n{text}")  # 打印信息，文本全为数字
        return False

    # 开头的字符内应该有数字，默认5个字符内
    if len(text) < 5:  # 如果文本长度小于5，直接使用文本
        text_5 = text
    else:
        text_5 = text[:5]  # 否则取前5个字符
    alpha_in_text_5 = sum(list(map(lambda x: x.isnumeric(), list(text_5))))  # 计算前5个字符中数字的数量
    if not alpha_in_text_5:  # 如果没有数字，返回False
        return False

    return True  # 如果通过所有检查，返回True

def zh_title_enhance(docs: Document) -> Document:
    """增强中文标题的识别和处理。

    遍历文档，识别可能的标题并进行标记。

    参数
    ----------
    docs
        输入的文档对象
    """
    title = None  # 初始化标题变量
    if len(docs) > 0:  # 如果文档不为空
        for doc in docs:  # 遍历每个文档
            if is_possible_title(doc.page_content):  # 如果文档内容可能是标题
                doc.metadata['category'] = 'cn_Title'  # 将文档元数据标记为中文标题
                title = doc.page_content  # 更新当前标题
            elif title:  # 如果存在标题
                doc.page_content = f"下文与({title})有关。{doc.page_content}"  # 在文档内容前添加与标题相关的信息
        return docs  # 返回处理后的文档
    else:
        print("文件不存在")  # 打印信息，文件不存在
