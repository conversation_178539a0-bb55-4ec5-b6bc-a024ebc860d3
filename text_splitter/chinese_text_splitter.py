"""
该模块实现了一个用于中文文本分割的类ChineseTextSplitter。该类继承自CharacterTextSplitter，
并提供了两种文本分割方法，分别是split_text1和split_text。通过正则表达式和自定义的分割逻辑，
可以将输入的文本按照句子进行分割，特别适用于处理PDF格式的文本。
"""

from langchain.text_splitter import CharacterTextSplitter  # 从langchain.text_splitter导入CharacterTextSplitter类
import re  # 导入正则表达式模块
from typing import List  # 导入List类型用于类型注解

class ChineseTextSplitter(CharacterTextSplitter):
    """用于中文文本分割的类，继承自CharacterTextSplitter。

    Attributes:
        pdf (bool): 指示是否处理PDF格式的文本。
        sentence_size (int): 指定分割后句子的最大长度。
    """

    def __init__(self, pdf: bool = False, sentence_size: int = 250, **kwargs):
        """初始化ChineseTextSplitter类的实例。

        Args:
            pdf (bool): 是否处理PDF格式的文本，默认为False。
            sentence_size (int): 分割后句子的最大长度，默认为250。
            **kwargs: 其他可选参数。
        """
        super().__init__(**kwargs)  # 调用父类的初始化方法
        self.pdf = pdf  # 设置pdf属性
        self.sentence_size = sentence_size  # 设置sentence_size属性

    def split_text1(self, text: str) -> List[str]:
        """使用正则表达式分割文本为句子列表。

        Args:
            text (str): 要分割的输入文本。

        Returns:
            List[str]: 分割后的句子列表。
        """
        if self.pdf:  # 如果处理PDF格式的文本
            text = re.sub(r"\n{3,}", "\n", text)  # 替换连续三个及以上的换行符为单个换行符
            text = re.sub('\s', ' ', text)  # 将所有空白字符替换为空格
            text = text.replace("\n\n", "")  # 移除双换行符

        # 定义正则表达式模式，用于匹配句子分隔符
        sent_sep_pattern = re.compile('([﹒﹔﹖﹗．。！？]["’”」』]{0,2}|(?=["‘“「『]{1,2}|$))')
        sent_list = []  # 初始化句子列表
        for ele in sent_sep_pattern.split(text):  # 遍历分割后的文本元素
            if sent_sep_pattern.match(ele) and sent_list:  # 如果元素匹配分隔符且句子列表不为空
                sent_list[-1] += ele  # 将元素添加到句子列表的最后一个元素
            elif ele:  # 如果元素不为空
                sent_list.append(ele)  # 将元素添加到句子列表
        return sent_list  # 返回句子列表

    def split_text(self, text: str) -> List[str]:
        """进一步优化的文本分割方法。

        Args:
            text (str): 要分割的输入文本。

        Returns:
            List[str]: 分割后的句子列表。
        """
        if self.pdf:  # 如果处理PDF格式的文本
            text = re.sub(r"\n{3,}", r"\n", text)  # 替换连续三个及以上的换行符为单个换行符
            text = re.sub('\s', " ", text)  # 将所有空白字符替换为空格
            text = re.sub("\n\n", "", text)  # 移除双换行符

        # 使用正则表达式进行多种模式的文本分割
        text = re.sub(r'([;；.!?。！？\?])([^”’])', r"\1\n\2", text)  # 单字符断句符
        text = re.sub(r'(\.{6})([^"’”」』])', r"\1\n\2", text)  # 英文省略号
        text = re.sub(r'(\…{2})([^"’”」』])', r"\1\n\2", text)  # 中文省略号
        text = re.sub(r'([;；!?。！？\?]["’”」』]{0,2})([^;；!?，。！？\?])', r'\1\n\2', text)
        # 如果双引号前有终止符，那么双引号才是句子的终点，把分句符\n放到双引号后，注意前面的几句都小心保留了双引号
        text = text.rstrip()  # 段尾如果有多余的\n就去掉它
        # 很多规则中会考虑分号;，但是这里我把它忽略不计，破折号、英文双引号等同样忽略，需要的再做些简单调整即可。
        ls = [i for i in text.split("\n") if i]  # 按换行符分割文本并移除空元素
        for ele in ls:  # 遍历分割后的文本元素
            if len(ele) > self.sentence_size:  # 如果元素长度超过设定的句子大小
                ele1 = re.sub(r'([,，.]["’”」』]{0,2})([^,，.])', r'\1\n\2', ele)  # 进一步分割
                ele1_ls = ele1.split("\n")  # 按换行符分割
                for ele_ele1 in ele1_ls:  # 遍历分割后的元素
                    if len(ele_ele1) > self.sentence_size:  # 如果元素长度仍然超过设定的句子大小
                        ele_ele2 = re.sub(r'([\n]{1,}| {2,}["’”」』]{0,2})([^\s])', r'\1\n\2', ele_ele1)  # 进一步分割
                        ele2_ls = ele_ele2.split("\n")  # 按换行符分割
                        for ele_ele2 in ele2_ls:  # 遍历分割后的元素
                            if len(ele_ele2) > self.sentence_size:  # 如果元素长度仍然超过设定的句子大小
                                ele_ele3 = re.sub('( ["’”」』]{0,2})([^ ])', r'\1\n\2', ele_ele2)  # 进一步分割
                                ele2_id = ele2_ls.index(ele_ele2)  # 获取当前元素的索引
                                ele2_ls = ele2_ls[:ele2_id] + [i for i in ele_ele3.split("\n") if i] + ele2_ls[ele2_id + 1:]  # 更新列表
                        ele_id = ele1_ls.index(ele_ele1)  # 获取当前元素的索引
                        ele1_ls = ele1_ls[:ele_id] + [i for i in ele2_ls if i] + ele1_ls[ele_id + 1:]  # 更新列表

                id = ls.index(ele)  # 获取当前元素的索引
                ls = ls[:id] + [i for i in ele1_ls if i] + ls[id + 1:]  # 更新列表
        return ls  # 返回分割后的句子列表
