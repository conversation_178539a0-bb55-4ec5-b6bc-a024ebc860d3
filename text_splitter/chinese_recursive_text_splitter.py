"""
该代码文件实现了一个中文文本分割器 `ChineseRecursiveTextSplitter`，用于将中文文本分割成指定大小的块。
它继承并优化了LangChain的 `RecursiveCharacterTextSplitter`，并针对中文文本的特点定义了特定的分隔符。

### 主要功能
- **分隔符定义**：定义了一组适用于中文文本的分隔符，包括段落分隔符（如 `\n\n`）、句子分隔符（如 `\n`）、中文标点符号（如 `。`、`！`、`？`）等。
- **文本分割逻辑**：通过递归方式，使用定义的分隔符将文本分割成小块。分割过程中，会选择合适的附加分隔符，确保每个文本块的大小不超过指定的 `chunk_size`。
- **保持分隔符**：提供选项以在分割后的文本块中保留分隔符。
- **示例演示**：在主函数中，提供了一个示例，展示了如何使用该分割器将一个长中文文本分割成指定大小的块，并打印每个块的内容。

### 实现逻辑
1. **分隔符选择**：从预定义的分隔符列表中选择一个合适的附加分隔符。优先选择列表中靠后的分隔符（即更细粒度的分隔符），如果文本中不存在该分隔符，则向前选择更粗粒度的分隔符。
2. **文本分割**：使用选定的分隔符将文本分割成小块。如果某个块仍然超过 `chunk_size`，则对该块递归应用更细粒度的分隔符进行分割。
3. **块合并**：对于小于 `chunk_size` 的块，使用分隔符将它们合并成一个块，确保合并后的块大小不超过 `chunk_size`。
4. **后处理**：去除块中的多余空行，并确保块不为空。

该分割器特别适用于处理中文文本，能够根据中文的标点和段落结构进行智能分割，确保分割后的文本块既保持语义连贯性，又满足大小限制。
"""

import re  # 导入正则表达式模块，用于文本分割
from typing import List, Optional, Any  # 导入类型提示相关工具
from langchain.text_splitter import RecursiveCharacterTextSplitter  # 导入基础递归文本分割器类
import logging  # 导入日志模块，用于记录日志

logger = logging.getLogger(__name__)  # 获取当前模块的日志记录器

def _split_text_with_regex_from_end(
        text: str, separator: str, keep_separator: bool
) -> List[str]:
    """使用正则表达式从文本末尾开始分割文本。

    参数：
        text: 要分割的文本。
        separator: 分隔符，可以是正则表达式。
        keep_separator: 是否在分割后的结果中保留分隔符。

    返回：
        分割后的文本列表。
    """
    # 现在我们有了分隔符，开始分割文本
    if separator:  # 检查分隔符是否非空
        if keep_separator:  # 如果需要保留分隔符
            # 模式中的括号会将分隔符保留在结果中
            _splits = re.split(f"({separator})", text)  # 使用正则表达式分割文本，分隔符被括号捕获
            splits = ["".join(i) for i in zip(_splits[0::2], _splits[1::2])]  # 将文本和分隔符配对合并
            if len(_splits) % 2 == 1:  # 如果分割结果长度为奇数，说明有未配对的文本
                splits += _splits[-1:]  # 将最后一个未配对的文本添加到结果中
            # splits = [_splits[0]] + splits  # 被注释掉的代码，未执行
        else:  # 如果不需要保留分隔符
            splits = re.split(separator, text)  # 直接使用分隔符分割文本，不保留分隔符
    else:  # 如果分隔符为空
        splits = list(text)  # 将文本逐字符分割成列表
    return [s for s in splits if s != ""]  # 过滤掉空字符串并返回最终列表

class ChineseRecursiveTextSplitter(RecursiveCharacterTextSplitter):
    """一个专门用于中文文本的递归文本分割器。

    该类继承自 `RecursiveCharacterTextSplitter`，并针对中文文本的特点定义了特定的分隔符。

    属性：
        _separators: 分隔符列表。
        _is_separator_regex: 指示分隔符是否为正则表达式的标志。

    主要功能包括：
        - 使用中文特有的分隔符（如中文标点符号）进行文本分割。
        - 支持递归分割，确保每个文本块的大小不超过指定值。
        - 提供选项以在分割后的文本块中保留分隔符。
    """
    def __init__(
            self,
            separators: Optional[List[str]] = None,
            keep_separator: bool = True,
            is_separator_regex: bool = True,
            **kwargs: Any,
    ) -> None:
        """创建一个新的文本分割器。

        参数：
            separators: 可选的分隔符列表。如果未提供，将使用默认的中文分隔符。
            keep_separator: 是否在分割后的文本块中保留分隔符。
            is_separator_regex: 分隔符是否为正则表达式。
            **kwargs: 传递给父类的其他参数。
        """
        super().__init__(keep_separator=keep_separator, **kwargs)  # 调用父类的初始化方法
        self._separators = separators or [  # 设置分隔符列表，若未提供则使用默认值
            "\n\n",  # 段落分隔符
            "\n",    # 句子分隔符
            "。|！|？",  # 中文句号、感叹号、问号
            "\.\s|\!\s|\?\s",  # 英文句号、感叹号、问号后跟空格
            "；|;\s",  # 中英文分号
            "，|,\s"   # 中英文逗号
        ]
        self._is_separator_regex = is_separator_regex  # 设置分隔符是否为正则表达式的标志

    def _split_text(self, text: str, separators: List[str]) -> List[str]:
        """分割传入的文本并返回文本块列表。

        参数：
            text: 要分割的文本。
            separators: 分隔符列表。

        返回：
            分割后的文本块列表。
        """
        final_chunks = []  # 初始化最终的文本块列表
        # 获取要使用的适当分隔符
        separator = separators[-1]  # 默认选择最后一个分隔符
        new_separators = []  # 初始化新的分隔符列表，用于后续递归
        for i, _s in enumerate(separators):  # 遍历分隔符列表
            _separator = _s if self._is_separator_regex else re.escape(_s)  # 如果不是正则表达式，则转义分隔符
            if _s == "":  # 如果分隔符为空
                separator = _s  # 直接使用空分隔符
                break  # 退出循环
            if re.search(_separator, text):  # 如果文本中存在当前分隔符
                separatorjor = _s  # 选择当前分隔符
                new_separators = separators[i + 1:]  # 更新新的分隔符列表为当前分隔符之后的
                break  # 退出循环

        _separator = separator if self._is_separator_regex else re.escape(separator)  # 确定用于分割的分隔符格式
        splits = _split_text_with_regex_from_end(text, _separator, self._keep_separator)  # 使用选定分隔符分割文本

        # 现在开始合并文本块，递归分割较长的文本
        _good_splits = []  # 初始化用于存放小于 chunk_size 的文本块列表
        _separator = "" if self._keep_separator else separator  # 确定合并时使用的分隔符
        for s in splits:  # 遍历分割后的文本块
            if self._length_function(s) < self._chunk_size:  # 如果当前文本块小于 chunk_size
                _good_splits.append(s)  # 将其添加到 _good_splits
            else:  # 如果当前文本块大于或等于 chunk_size
                if _good_splits:  # 如果 _good_splits 不为空
                    merged_text = self._merge_splits(_good_splits, _separator)  # 合并 _good_splits 中的文本块
                    final_chunks.extend(merged_text)  # 将合并结果添加到 final_chunks
                    _good_splits = []  # 清空 _good_splits
                if not new_separators:  # 如果没有新的分隔符可用
                    final_chunks.append(s)  # 直接将当前文本块添加到 final_chunks
                else:  # 如果有新的分隔符
                    other_info = self._split_text(s, new_separators)  # 递归分割当前文本块
                    final_chunks.extend(other_info)  # 将递归分割结果添加到 final_chunks
        if _good_splits:  # 如果 _good_splits 中仍有剩余文本块
            merged_text = self._merge_splits(_good_splits, _separator)  # 合并剩余的文本块
            final_chunks.extend(merged_text)  # 将合并结果添加到 final_chunks
        return [re.sub(r"\n{2,}", "\n", chunk.strip()) for chunk in final_chunks if chunk.strip()!=""]  # 后处理：去除多余空行并过滤空块

if __name__ == "__main__":
    text_splitter = ChineseRecursiveTextSplitter(  # 创建 ChineseRecursiveTextSplitter 实例
        keep_separator=True,  # 设置保留分隔符
        is_separator_regex=True,  # 设置分隔符为正则表达式
        chunk_size=50,  # 设置每个文本块的最大大小
        chunk_overlap=0  # 设置文本块之间的重叠大小
    )
    ls = [  # 定义要分割的文本列表
        """中国对外贸易形势报告（75页）。前 10 个月，一般贸易进出口 19.5 万亿元，增长 25.1%， 比整体进出口增速高出 2.9 个百分点，占进出口总额的 61.7%，较去年同期提升 1.6 个百分点。其中，一般贸易出口 10.6 万亿元，增长 25.3%，占出口总额的 60.9%，提升 1.5 个百分点；进口8.9万亿元，增长24.9%，占进口总额的62.7%， 提升 1.8 个百分点。加工贸易进出口 6.8 万亿元，增长 11.8%， 占进出口总额的 21.5%，减少 2.0 个百分点。其中，出口增 长 10.4%，占出口总额的 24.3%，减少 2.6 个百分点；进口增 长 14.2%，占进口总额的 18.0%，减少 1.2 个百分点。此外， 以保税物流方式进出口 3.96 万亿元，增长 27.9%。其中，出 口 1.47 万亿元，增长 38.9%；进口 2.49 万亿元，增长 22.2%。前三季度，中国服务贸易继续保持快速增长态势。服务 进出口总额 37834.3 亿元，增长 11.6%；其中服务出口 17820.9 亿元，增长 27.3%；进口 20013.4 亿元，增长 0.5%，进口增 速实现了疫情以来的首次转正。服务出口增幅大于进口 26.8 个百分点，带动服务贸易逆差下降 62.9%至 2192.5 亿元。服 务贸易结构持续优化，知识密集型服务进出口 16917.7 亿元， 增长 13.3%，占服务进出口总额的比重达到 44.7%，提升 0.7 个百分点。 二、中国对外贸易发展环境分析和展望 全球疫情起伏反复，经济复苏分化加剧，大宗商品价格 上涨、能源紧缺、运力紧张及发达经济体政策调整外溢等风 险交织叠加。同时也要看到，我国经济长期向好的趋势没有 改变，外贸企业韧性和活力不断增强，新业态新模式加快发 展，创新转型步伐提速。产业链供应链面临挑战。美欧等加快出台制造业回迁计 划，加速产业链供应链本土布局，跨国公司调整产业链供应 链，全球双链面临新一轮重构，区域化、近岸化、本土化、 短链化趋势凸显。疫苗供应不足，制造业“缺芯”、物流受限、 运价高企，全球产业链供应链面临压力。 全球通胀持续高位运行。能源价格上涨加大主要经济体 的通胀压力，增加全球经济复苏的不确定性。世界银行今年 10 月发布《大宗商品市场展望》指出，能源价格在 2021 年 大涨逾 80%，并且仍将在 2022 年小幅上涨。IMF 指出，全 球通胀上行风险加剧，通胀前景存在巨大不确定性。""",
    ]
    # text = """"""  # 被注释掉的空文本定义，未使用
    for inum, text in enumerate(ls):  # 遍历文本列表
        print(inum)  # 打印当前文本的编号
        chunks = text_splitter.split_text(text)  # 分割当前文本
        for chunk in chunks:  # 遍历分割后的文本块
            print(chunk)  # 打印每个文本块