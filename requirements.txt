torch==2.0.1 #torch>=2.0.1
torchvision
torchaudio
xformers==0.0.22 # xformers>=0.0.22.post4

langchain>=0.0.319
langchain-experimental>=0.0.30
fschat[model_worker]==0.2.31
openai>=0.28.1
sentence_transformers
transformers>=4.34
fastapi>=0.104
nltk>=3.8.1
uvicorn~=0.23.1
starlette~=0.27.0
pydantic~=1.10.11
unstructured[all-docs]>=0.10.12
python-magic-bin; sys_platform == 'win32'
SQLAlchemy==2.0.19
faiss-cpu
accelerate
spacy
PyMuPDF
rapidocr_onnxruntime

requests
pathlib
pytest
scikit-learn
numexpr
vllm>=0.2.0; sys_platform == "linux"
# online api libs
# zhipuai
# dashscope>=1.10.0 # qwen
# qianfan
# volcengine>=1.0.106 # fangzhou

# uncomment libs if you want to use corresponding vector store
# pymilvus==2.1.3 # requires milvus==2.1.3
# psycopg2
# pgvector

numpy~=1.24.4
pandas~=2.0.3
streamlit>=1.26.0
streamlit-option-menu>=0.3.6
streamlit-antd-components>=0.1.11
streamlit-chatbox==1.1.10
streamlit-aggrid>=0.3.4.post3
httpx~=0.24.1
watchdog
tqdm
websockets
tiktoken
einops
scipy
transformers_stream_generator==0.0.4
