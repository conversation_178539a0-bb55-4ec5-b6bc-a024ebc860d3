# 该文件是一个基于Streamlit的Web用户界面，用于政务行业AI助手的实现。
# 主要功能包括页面配置、侧边栏导航、页面选择和密码验证等。

import streamlit as st  # 导入Streamlit库，用于创建Web应用
from webui_pages.utils import *  # 导入webui_pages.utils模块中的所有内容
from streamlit_option_menu import option_menu  # 导入option_menu，用于创建选项菜单
from webui_pages import *  # 导入webui_pages模块中的所有内容
import os  # 导入os模块，用于操作系统相关功能
from configs import VERSION  # 从configs模块导入VERSION变量
from server.utils import api_address  # 从server.utils模块导入api_address函数

# 单独调试时，需要取消下面代码的注释
# os.chdir(os.path.dirname(os.path.abspath(__file__)))  # 将当前工作目录更改为文件所在目录


# 创建一个ApiRequest对象，并将其赋值给变量api。
# ApiRequest是一个用于发送API请求的类，通过传入的base_url参数设置基础URL。
# 这里的base_url是通过调用api_address()函数获取的，该函数返回API服务器的地址。
api = ApiRequest(base_url=api_address())  

# TODO: 这样可能存在泄露风险，后续需要考虑将密码编码到环境变量里面。
ADMIN_PASSWORD = "123"  # 设置管理员密码为123

if __name__ == "__main__":  # 判断是否为主程序入口
    st.set_page_config(  # 设置页面配置
        "政务行业AI助手3.0",  # 设置页面标题
        os.path.join("img", "assistant-icon.png"),  # 设置页面图标
        initial_sidebar_state="expanded",  # 设置侧边栏初始状态为展开
        menu_items={  # 设置菜单项
            'Get Help': 'https://github.com/chatchat-space/Langchain-Chatchat',  # 帮助链接
            'Report a bug': "https://github.com/chatchat-space/Langchain-Chatchat/issues",  # 报告错误链接
            'About': f"""欢迎使用 Langchain-Chatchat WebUI {VERSION}！"""  # 关于信息
        }
    )

    pages = {  # 定义页面字典
        "对话": {  # 对话页面
            "icon": "chat",  # 图标为聊天
            "func": dialogue_page,  # 对应的函数为dialogue_page
        },
        "知识库管理": {  # 知识库管理页面
            "icon": "hdd-stack",  # 图标为硬盘堆栈
            "func": knowledge_base_page,  # 对应的函数为knowledge_base_page
        },
        "事故调查报告结构化": {  # 事故调查报告结构化页面
            "icon": "book",  # 图标为书本
            "func": accident_report_to_structure_page,  # 对应的函数为accident_report_to_structure_page
        },
        "事故调查报告结构化-批量": {  # 事故调查报告结构化-批量页面
            "icon": "book",  # 图标为书本
            "func": accident_report_to_structure_batch_page,  # 对应的函数为accident_report_to_structure_batch_page
        },
        "应急预案文件拆解": {  # 应急预案文件拆解页面
            "icon": "book",  # 图标为书本
            "func": disconstruct_page,  # 对应的函数为disconstruct_page
        },
        # "水利私域问答": {  # 水利私域问答页面（已注释）
        #     "icon": "book",  # 图标为书本
        #     "func": water_resources_page  # 对应的函数为water_resources_page
        # },
        # "应急预案生成": {  # 应急预案生成页面
        #     "icon": "book",  # 图标为书本
        #     "func": emergency_plan_generate_page  # 对应的函数为emergency_plan_generate_page
        # }
    }

    with st.sidebar:  # 在侧边栏中
        st.image(  # 显示图片
            os.path.join(
                "img",
                "logo-long-chatchat-trans-v2.png"
            ),
            use_column_width=True  # 使用列宽
            # use_container_width=True  # 使用容器宽度（已注释）

        )

        st.caption(  # 显示说明文字
            f"""<p align="right">政务行业AI助手2.0</p>""",  # 右对齐的说明文字
            unsafe_allow_html=True,  # 允许不安全的HTML
        )
        options = list(pages)  # 获取页面列表
        icons = [x["icon"] for x in pages.values()]  # 获取页面图标列表

        default_index = 0  # 默认选中第一个页面

        if 'current_page' not in st.session_state:  # 如果当前页面不存在，则设置为默认页面
            st.session_state.current_page = options[default_index]  # 设置当前页面为默认页面

        # 调用 option_menu 并传入一个唯一的 key
        selected_page = option_menu(  # 创建选项菜单
            "",
            options=options,  # 设置选项为页面列表
            icons=icons,  # 设置图标为页面图标列表
            default_index=options.index(st.session_state.current_page),  # 设置默认选项为当前页面
            key="sidebar_option_menu"  # 添加唯一的 key
        )

        if selected_page != st.session_state.current_page:  # 如果选中的页面与当前页面不同
            st.session_state.current_page = selected_page  # 更新当前页面为选中页面
            chat_box.reset_history()  # 重置聊天历史

        if selected_page == "知识库管理":  # 如果选中页面为知识库管理
            admin_password_input = st.text_input("知识库访问密码", type="password")  # 输入密码

            if admin_password_input and admin_password_input != ADMIN_PASSWORD:  # 如果输入的密码不正确
                st.error("密码错误，请重试！")  # 显示错误信息

    if selected_page == "知识库管理":  # 如果选中页面为知识库管理
        # 增加密码管理逻辑
        if admin_password_input == ADMIN_PASSWORD:  # 如果输入的密码正确
            pages[selected_page]["func"](api)  # 调用对应的页面函数
    else:
        pages[selected_page]["func"](api)  # 调用对应的页面函数