import streamlit as st
from webui_pages.utils import *
from streamlit_chatbox import *
from datetime import datetime
import os
from configs import (LLM_MODEL, TEMPERATURE, HISTORY_LEN, PROMPT_TEMPLATES,
                     DEFAULT_KNOWLEDGE_BASE, DEFAULT_SEARCH_ENGINE, LANGCHAIN_LLM_MODEL)
from typing import List, Dict
import json
from datetime import datetime
from rank_bm25 import BM25Okapi
import jieba
import re


# 静态变量统一放这
# 后续可考虑用excel表导入
ACCIDENT_TYPE = ["地震", "城市轨道交通", "地质灾害", "电力故障", "动物疫情", "防汛抗旱", "海上搜救", "核应急", "环境事件", "粮食应急", "民用航空", "生产事故", "食品安全", "铁路轨道交通", "通信保障", "突发事件", "危险货物", "医疗卫生"]
TYPE_TO_KB = {
                "地震": "emergency_plan_earthquake",
                "城市轨道交通": "emergency_plan_Urban rail transit",
                "地质灾害": "emergency_plan_Geological hazards",
                "电力故障": "emergency_plan_Power failure",
                "动物疫情": "emergency_plan_Animal epidemics",
                "防汛抗旱": "emergency_plan_Floods and droughts",
                "海上搜救": "emergency_plan_Search and rescue at sea",
                "核应急": "emergency_plan_nucleus",
                "环境事件": "emergency_plan_environment",
                "粮食应急": "emergency_plan_grain",
                "民用航空": "emergency_plan_civil aviation",
                "生产事故": "emergency_plan_Production accidents",
                "食品安全": "emergency_plan_Food safety",
                "铁路轨道交通": "emergency_plan_Rail transit",
                "通信保障": "emergency_plan_correspondence",
                "突发事件": "emergency_plan_Emergencies",
                "危险货物": "emergency_plan_Dangerous goods",
                "医疗卫生": "emergency_plan_medical",
                "危险货物": "emergency_plan_Dangerous goods",
                "自然灾害": "emergency_plan_natural disaster"
               }

LAW_LISTS = {
                "地震": ["《国家突发公共事件总体应急预案》", "《中华人民共和国突发事件应对法》", "《中华人民共和国安全生产法》", "《生产安全事故报告和调查处理条例》", "《中华人民共和国防震减灾法》", "《破坏性地震应急条例》", "《中央自然灾害救灾资金管理暂行办法》"],
                "城市轨道交通": ["《国家突发公共事件总体应急预案》", "《中华人民共和国突发事件应对法》", "《中华人民共和国安全生产法》", "《生产安全事故报告和调查处理条例》"],
                "地质灾害": ["《国家突发公共事件总体应急预案》", "《中华人民共和国突发事件应对法》", "《中华人民共和国安全生产法》", "《生产安全事故报告和调查处理条例》", "《地质灾害防治条例》", "《国家突发地质灾害应急预案》"],
                "电力故障": ["《国家突发公共事件总体应急预案》", "《中华人民共和国突发事件应对法》", "《中华人民共和国安全生产法》", "《生产安全事故报告和调查处理条例》", "《中华人民共和国电力法》"],
                "动物疫情": ["《国家突发公共事件总体应急预案》", "《中华人民共和国突发事件应对法》", "《中华人民共和国安全生产法》", "《生产安全事故报告和调查处理条例》", "《中华人民共和国动物防疫法》"],
                "防汛抗旱": ["《国家突发公共事件总体应急预案》", "《中华人民共和国突发事件应对法》", "《中华人民共和国安全生产法》", "《生产安全事故报告和调查处理条例》", "《中华人民共和国防洪法》", "《中华人民共和国抗旱条例》"],
                "海上搜救": ["《国家突发公共事件总体应急预案》", "《中华人民共和国突发事件应对法》", "《中华人民共和国安全生产法》", "《生产安全事故报告和调查处理条例》"],
                "核应急": ["《国家突发公共事件总体应急预案》", "《中华人民共和国突发事件应对法》", "《中华人民共和国安全生产法》", "《生产安全事故报告和调查处理条例》"],
                "环境事件": ["《国家突发公共事件总体应急预案》", "《中华人民共和国突发事件应对法》", "《中华人民共和国安全生产法》", "《生产安全事故报告和调查处理条例》", "《中华人民共和国环境保护法》"],
                "粮食应急": ["《国家突发公共事件总体应急预案》", "《中华人民共和国突发事件应对法》", "《中华人民共和国安全生产法》", "《生产安全事故报告和调查处理条例》", "《中华人民共和国价格法》"],
                "民用航空": ["《国家突发公共事件总体应急预案》", "《中华人民共和国突发事件应对法》", "《中华人民共和国安全生产法》", "《生产安全事故报告和调查处理条例》"],
                "生产事故": ["《国家突发公共事件总体应急预案》", "《中华人民共和国突发事件应对法》", "《中华人民共和国安全生产法》", "《生产安全事故报告和调查处理条例》"],
                "食品安全": ["《国家突发公共事件总体应急预案》", "《中华人民共和国突发事件应对法》", "《中华人民共和国安全生产法》", "《生产安全事故报告和调查处理条例》"],
                "铁路轨道交通": ["《国家突发公共事件总体应急预案》", "《中华人民共和国突发事件应对法》", "《中华人民共和国安全生产法》", "《生产安全事故报告和调查处理条例》"],
                "通信保障": ["《国家突发公共事件总体应急预案》", "《中华人民共和国突发事件应对法》", "《中华人民共和国安全生产法》", "《生产安全事故报告和调查处理条例》"],
                "突发事件": ["《国家突发公共事件总体应急预案》", "《中华人民共和国突发事件应对法》", "《中华人民共和国安全生产法》", "《生产安全事故报告和调查处理条例》"],
                "医疗卫生": ["《国家突发公共事件总体应急预案》", "《中华人民共和国突发事件应对法》", "《中华人民共和国安全生产法》", "《生产安全事故报告和调查处理条例》", "《医疗机构管理条例》"],
                "危险货物": ["《国家突发公共事件总体应急预案》", "《中华人民共和国突发事件应对法》", "《中华人民共和国安全生产法》", "《生产安全事故报告和调查处理条例》", "《中华人民共和国港口法》", "《危险化学品安全管理条例》", "《危险化学品重大危险源监督管理暂行办法》", "《港口危险货物管理规定》"],
                "自然灾害": ["《国家突发公共事件总体应急预案》", "《中华人民共和国突发事件应对法》", "《中华人民共和国安全生产法》", "《生产安全事故报告和调查处理条例》", "《自然灾害救助条例》", ]
}

if 'doc_content' not in st.session_state:
    st.session_state.doc_content = ""

def emergency_plan_generate_page(api: ApiRequest):
    # Streamlit 表单
    with open('emergency_plan_v2.json', 'r', encoding='utf-8') as json_file:
        data = json.load(json_file)
    st.title("应急预案信息填写表单")
    # 添加一些自定义的CSS来调整文本框的外观
    st.markdown("""
        <style>
        .stTextArea textarea, .stTextInput input {
            background-color: white !important;
            color: black !important;
            border: 1px solid #ccc !important;
            border-radius: 4px !important;
            padding: 10px !important;
        }
        </style>
        """, unsafe_allow_html=True)


    def radio_in_columns(label, options, cols):
        # 初始化选中的值
        if 'selected_option' not in st.session_state:
            st.session_state.selected_option = options[0]

        # 创建列
        columns = st.columns(cols)

        for i, option in enumerate(options):
            col = columns[i % cols]
            with col:
                if st.button(option, key=option):
                    st.session_state.selected_option = option

        # 返回选中的值
        return st.session_state.selected_option

    with st.expander("请选择事故类型（单选）", expanded=True):
        # 调用函数，将选项分成三列展示
        accident_type = radio_in_columns("事故类型", ACCIDENT_TYPE, 3)
        kb_name = TYPE_TO_KB[accident_type]
        st.write(f"选择的事故类型：{accident_type}")
        st.write(f"对应的知识库：{kb_name}")


    # 法律依据
    with st.expander(
            "请选择参考的法律依据",
            expanded=True,
    ):
        # 获取法律依据列表
        label_list = LAW_LISTS[accident_type]
        is_choose_label = []
        for i in range(len(label_list)):
            is_choose_label.append(False)

        select_all = st.checkbox("全选/取消全选")

        cols = st.columns(3)
        for i in range(label_list.__len__()):
            is_choose_label[i] = cols[i % 3].checkbox(label_list[i], select_all)

    with st.form("emergency_plan_form"):

        law = ""
        for i in range(len(label_list)):
            if is_choose_label[i]:
                law += label_list[i]
        department = st.text_area("预案编写部门")
        applicable_area = st.text_area("适用区域（全国、某省、某市、某区、某县等）")


        # 提交按钮
        submitted = st.form_submit_button("提交")

        if submitted:
            print("--------------")
            print(law)
            print("--------------")
            print(accident_type)
            print("--------------")
            # 更新数据字典

            # law
            data["总则"]["编制依据"]["usr_input"] = law
            # accident_type
            data["总则"]["编制目的"]["usr_input"] = accident_type
            data["总则"]["工作原则"]["usr_input"] = accident_type
            data["总则"]["事件分级"]["usr_input"] = accident_type
            data["应急响应"]["国家层面的应对工作"]["usr_input"] = accident_type
            data["应急响应"]["指挥和调度"]["usr_input"] = accident_type
            data["应急响应"]["应急终止"]["usr_input"] = accident_type
            data["应急保障"]["通信与信息保障"]["usr_input"] = accident_type
            data["应急保障"]["队伍、装备和物资保障"]["usr_input"] = accident_type
            data["应急保障"]["技术支持"]["usr_input"] = accident_type
            data["应急保障"]["交通与运输保障"]["usr_input"] = accident_type
            data["应急保障"]["资金保障"]["usr_input"] = accident_type
            data["后期处置"]["工作总结和改进措施"]["usr_input"] = accident_type
            data["应急保障"]["培训和演练（宣传、教育）"]["usr_input"] = accident_type
            data["监测预警和信息报告"]["监测和风险分析"]["usr_input"] = accident_type
            data["组织指挥体系"]["国家层面组织指挥机构"]["usr_input"] = accident_type
            data["附则"]["预案实施时间"]["usr_input"] = accident_type
            # department
            data["监测预警和信息报告"]["预警机制"]["usr_input"] = department
            data["后期处置"]["事件调查与评估"]["usr_input"] = department
            data["后期处置"]["善后处置和恢复重建"]["usr_input"] = department
            data["附则"]["预案管理"]["usr_input"] = department
            data["附则"]["预案解释"]["usr_input"] = department
            # 复合
            data["总则"]["适用范围"]["usr_input"] = ",".join([applicable_area, department])
            data["组织指挥体系"]["地方层面组织指挥机构"]["usr_input"] = ",".join([accident_type, department])
            data["组织指挥体系"]["现场指挥机构"]["usr_input"] = ",".join([accident_type, department])


            # 将更新后的数据写回 JSON 文件
            with open('emergency_plan_v2.json', 'w', encoding='utf-8') as json_file:
                json.dump(data, json_file, ensure_ascii=False, indent=4)

            st.success("信息已成功提交并保存到 JSON 文件中。")

    # 最终生成的文档内容
    doc_content = ""
    # 一级标题编号
    cap_first = 0

    cols_g = st.columns(1)
    if cols_g[0].button(
            "点击开始应急预案文件生成",
            use_container_width=True,
    ):
        # 应急预案文件生成
        # 一些配置
        prompt_templates_kb_list = list(PROMPT_TEMPLATES["empty"].keys())
        prompt_template_name = prompt_templates_kb_list[0]

        with open('emergency_plan_v2.json', 'r', encoding='utf-8') as json_file:
            format_data = json.load(json_file)

        # 一级标题
        for title_1, content_1 in format_data.items():
            cap_first = cap_first + 1
            doc_content = doc_content + '\n' + str(cap_first) + ' ' + title_1 + '\n'
            # 二级标题编号
            cap_second = 0
            # 二级标题
            for title_2, content_2 in format_data[title_1].items():
                cap_second = cap_second + 1
                doc_content = doc_content + str(cap_first) + '.' + str(cap_second) + ' ' + title_2 + '\n'
                # 取二级标题，调用对应事故类型的知识库进行检索，并按照得分进行排序
                text = ""
                for d in api.knowledge_base_chat(title_2,
                                                 knowledge_base_name=kb_name,
                                                 top_k=8,
                                                 score_threshold=1,
                                                 model=LLM_MODEL,
                                                 prompt_name=prompt_template_name,
                                                 temperature=0.05):
                    if error_msg := check_error_msg(d):  # check whether error occured
                        st.error(error_msg)
                    elif chunk := d.get("answer"):
                        text += chunk

                docs = d.get("docs", [])
                text_contents = []
                for doc in docs:
                    # 分割字符串以提取text部分
                    parts = doc.split('参考： ')
                    if len(parts) > 1:
                        text_content = parts[1]
                        text_contents.append(text_content)

                # 使用jieba进行中文分词
                tokenized_texts = [list(jieba.cut(text_content)) for text_content in text_contents if
                                   text_content.strip()]  # 过滤空文本
                tokenized_texts = [text for text in tokenized_texts if text]  # 去除空列表

                # 新增判断：如果处理后仍然没有有效文本，跳过BM25处理
                if not tokenized_texts:
                    sorted_docs = docs  # 直接使用原始文档
                else:
                    tokenized_query = list(jieba.cut(text))
                    # BM25向量化
                    bm25 = BM25Okapi(tokenized_texts, b=1, k1=1)
                    # 计算BM25分数
                    scores = bm25.get_scores(tokenized_query)
                    # 将原始文档(docs)与它们的BM25分数关联起来
                    docs_with_scores = [(doc, scores[index]) for index, doc in enumerate(docs)]
                    # 根据BM25分数降序排序
                    sorted_docs_with_scores = sorted(docs_with_scores, key=lambda x: x[1], reverse=True)
                    # 提取排序后的文档
                    sorted_docs = [doc for doc, score in sorted_docs_with_scores]

                # 新增判断：确保sorted_docs不为空
                if sorted_docs:
                    # 取得分最高的一条检索信息
                    pattern = r"\n\n参考: .*?："
                    kb_data = re.sub(pattern, "", sorted_docs[0])
                else:
                    kb_data = "暂无相关参考内容"  # 添加默认值
                
                content_2["content"] = kb_data
                # 取输入内容input以及提示词模版prompt
                if "content" in content_2:
                    content = content_2["content"]
                else:
                    continue
                if "prompt" in content_2:
                    prompt = content_2["prompt"]
                else:
                    continue
                if "usr_input" in content_2:
                    usr_input = content_2["usr_input"]
                else:
                    continue
                # 组合模版内容、提示词模版、用户输入，调用大模型输出
                generate_data = ""
                r = api.chat_chat(query="{0}\n{1}\n{2}".format(content, prompt, usr_input),
                                  model=LLM_MODEL,
                                  prompt_name=prompt_template_name,
                                  temperature=0.05)
                for t in r:
                    if error_msg := check_error_msg(t):  # check whether error occured
                        st.error(error_msg)
                        break
                    generate_data += t
                generate_data.replace('\r\n', '\n').replace('\n\r', '\n').replace('\r', '\n').replace('\n\n', '\n')
                generate_data = generate_data.strip()

                # 将生成的内容放进json文件对象并存入json文件
                print("generate_data: {}".format(generate_data))
                format_data[title_1][title_2]["generate_data"] = generate_data
                with open('emergency_plan_v2.json', 'w', encoding='utf-8') as json_file:
                    json.dump(format_data, json_file, ensure_ascii=False, indent=4)
                doc_content = doc_content + format_data[title_1][title_2]["generate_data"] + '\n'

        print(doc_content)
        st.session_state.doc_content = doc_content  # 将生成内容存入session

        cols_d = st.columns(1)
        with cols_d[0]:
            if st.session_state.doc_content:
                export_btn = st.columns(1)
                export_btn[0].download_button(
                    "点击下载应急预案文件",
                    st.session_state.doc_content,
                    file_name=f"{datetime.now():%Y-%m-%d %H.%M}_应急预案.docx",
                    mime="text/docx",
                    use_container_width=True,
                )
            else:
                st.warning("请先点击生成按钮创建应急预案文档")
