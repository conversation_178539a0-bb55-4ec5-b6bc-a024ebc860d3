import streamlit as st
from webui_pages.utils import *
from streamlit_chatbox import *
from datetime import datetime
import os
from configs import (LLM_MODEL, TEMPERATURE, HISTORY_LEN, PROMPT_TEMPLATES,
                     DEFAULT_KNOWLEDGE_BASE, DEFAULT_SEARCH_ENGINE,LANGCHAIN_LLM_MODEL)
from typing import List, Dict
import csv
import re
from server.knowledge_base.kb_service.base import get_kb_details, get_kb_file_details
from server.knowledge_base.utils import get_file_path, LOADER_DICT
import pandas as pd
from server.knowledge_base.kb_doc_api import search_docs


chat_box = ChatBox(
    user_avatar=os.path.join(
        "img",
        "user-icon.png"
    ),
    assistant_avatar=os.path.join(
        "img",
        "assistant-icon.png"
    )
)

def get_messages_history(history_len: int, content_in_expander: bool = False) -> List[Dict]:
    '''
    获取 streamlit_chatbox 组件的历史记录，整理成 List[Dict] 形式。
    其中，Assistant 的消息会移除 Markdown 标题。
    '''
    def filter(msg):
        # 仅保留 markdown 和 text 类型的元素
        content = [x for x in msg["elements"] if x._output_method in ["markdown", "text"]]
        # 可选：是否包含折叠框内的内容
        if not content_in_expander:
            content = [x for x in content if not x._in_expander]
        # 提取元素内容
        content = [x.content for x in content]
        
        # 合并内容
        content_text = "\n\n".join(content)
        
        # 获取角色
        role = msg.get("role", "")

        # 对 Assistant 的消息内容进行清洗，移除可能导致解析错误的 Markdown 标题
        if role == "assistant":
            # 移除 Markdown 标题 (###, ##, #)
            content_text = re.sub(r"(?m)^#+\s+.*\n?", "", content_text)
            # 移除粗体和斜体标记，但保留内容
            content_text = re.sub(r"\*\*([^*]+)\*\*", r"\1", content_text)
            content_text = re.sub(r"\*([^*]+)\*", r"\1", content_text)
            # 移除列表标记，但保留内容
            content_text = re.sub(r"(?m)^[-\*+]\s+", "", content_text)
            # 移除多余的换行符
            content_text = re.sub(r"\n{3,}", "\n\n", content_text).strip()

        # 确保只返回 'user' 或 'assistant' 角色，并且 content 是字符串
        if role in ["user", "assistant"] and isinstance(content_text, str):
            return {
                "role": role,
                "content": content_text,
            }
        else:
            return None # 过滤掉不符合格式或角色的消息

    # 使用 filter 函数过滤历史记录
    history = chat_box.filter_history(history_len=history_len, filter=filter)
    # 再次过滤掉 filter 函数返回 None 的结果
    valid_history = [msg for msg in history if msg is not None]
    return valid_history


def get_default_llm_model(api: ApiRequest) -> (str, bool):
    running_models = api.list_running_models()
    if not running_models:
        return "", False

    if LLM_MODEL in running_models:
        return LLM_MODEL, True

    local_models = [k for k, v in running_models.items() if not v.get("online_api")]
    if local_models:
        return local_models[0], True
    return list(running_models)[0], False


def accident_report_to_structure_batch_page(api: ApiRequest):
    kb = "temporary_accident"
    kb_list = {x["kb_name"]: x for x in get_kb_details()}
    st.session_state["selected_kb_info"] = kb_list[kb]['kb_info']
    languages = {
        "CN": {
            "button": "浏览文件",
            "instructions": "将文件拖放到此处",
            "limits": "每个文件限制 200MB",
        },
    }
    lang = "CN"
    hide_label = (
        """
        <style>
            div[data-testid="stFileUploader"]>section[data-testid="stFileUploadDropzone"]>button[data-testid="baseButton-secondary"] {
               color:white;
            }
            div[data-testid="stFileUploader"]>section[data-testid="stFileUploadDropzone"]>button[data-testid="baseButton-secondary"]::after {
                content: "BUTTON_TEXT";
                color:black;
                display: block;
                position: absolute;
            }
            div[data-testid="stFileDropzoneInstructions"]>div>span {
               visibility:hidden;
            }
            div[data-testid="stFileDropzoneInstructions"]>div>span::after {
               content:"INSTRUCTIONS_TEXT";
               visibility:visible;
               display:block;
            }
            div[data-testid="stFileDropzoneInstructions"]>div>small {
               visibility:hidden;
            }
            div[data-testid="stFileDropzoneInstructions"]>div>small::before {
               content:"FILE_LIMITS";
               visibility:visible;
               display:block;
            }
        </style>
        """.replace(
            "BUTTON_TEXT", languages.get(lang).get("button")
        )
        .replace("INSTRUCTIONS_TEXT", languages.get(lang).get("instructions"))
        .replace("FILE_LIMITS", languages.get(lang).get("limits"))
    )
    st.markdown(hide_label, unsafe_allow_html=True)
    kb = "temporary_accident"
    kb_list = {x["kb_name"]: x for x in get_kb_details()}
    st.session_state["selected_kb_info"] = kb_list[kb]['kb_info']
    files = st.file_uploader("上传需要标签化的事故调查报告（文字型的PDF文件）：",
                             [i for ls in LOADER_DICT.values() for i in ls],    # 支持上传哪些文件，这里不用修改（已经失效）
                             accept_multiple_files=True, # 这个是否只是多文件上传，我们只需要一次分析一个文件，因此这里应该设置为false
                             # accept_multiple_files=False,
                             )
    chunk_size = CHUNK_SIZE
    chunk_overlap = OVERLAP_SIZE
    zh_title_enhance = ZH_TITLE_ENHANCE
    label_list = []
    is_choose_label = []
    with open(r'P:\Workspace\Python\zhengwuhangyezhushou3\事故标签体系问题集.csv', encoding='gb2312') as f:  # 替换 示例：r'F:\zhongxing\事故标签体系问题集.csv'
        reader = list(csv.reader(f))
        for row in reader:
            while row[-1] == '':
                row.pop(-1)
            label_list.append(f"{row[0]}")

            is_choose_label.append(True)
    is_choose_label[0] = False
    is_choose_label[4] = False
    is_choose_label[6] = False
    is_choose_label[7] = False

    if not chat_box.chat_inited:
        default_model = get_default_llm_model(api)[0]
        st.toast(
            "欢迎使用 政务行业AI助手2.0 ! \n\n"
        )
        chat_box.init_session()
    with st.sidebar:
        # TODO: 对话模型与会话绑定
        def on_mode_change():
            mode = st.session_state.dialogue_mode
            text = f"已切换到 {mode} 模式。"
            if mode == "法律知识问答":
                cur_kb = st.session_state.get("selected_kb")
                if cur_kb:
                    text = f"{text} 当前知识库： `{cur_kb}`。"
            st.toast(text)

        dialogue_mode = "事故调查报告结构化-批量"

        def on_llm_change():
            if llm_model:
                config = api.get_model_config(llm_model)
                if not config.get("online_api"):  # 只有本地model_worker可以切换模型
                    st.session_state["prev_llm_model"] = llm_model
                st.session_state["cur_llm_model"] = st.session_state.llm_model

        def llm_model_format_func(x):
            if x in running_models:
                return f"{x} (Running)"
            return x

        running_models = list(api.list_running_models())
        running_models += LANGCHAIN_LLM_MODEL.keys()
        available_models = []
        config_models = api.list_config_models()
        worker_models = list(config_models.get("worker", {}))  # 仅列出在FSCHAT_MODEL_WORKERS中配置的模型
        for m in worker_models:
            if m not in running_models and m != "default":
                available_models.append(m)
        for k, v in config_models.get("online", {}).items():  # 列出ONLINE_MODELS中直接访问的模型
            if not v.get("provider") and k not in running_models:
                available_models.append(k)
        for k, v in config_models.get("langchain", {}).items():  # 列出LANGCHAIN_LLM_MODEL支持的模型
            available_models.append(k)
        llm_models = running_models + available_models
        index = llm_models.index(st.session_state.get("cur_llm_model", get_default_llm_model(api)[0]))
        llm_model = LLM_MODEL
        if (st.session_state.get("prev_llm_model") != llm_model
                and not llm_model in config_models.get("online", {})
                and not llm_model in config_models.get("langchain", {})
                and llm_model not in running_models):
            with st.spinner(f"正在加载模型： {llm_model}，请勿进行操作或刷新页面"):
                prev_model = st.session_state.get("prev_llm_model")
                r = api.change_llm_model(prev_model, llm_model)
                if msg := check_error_msg(r):
                    st.error(msg)
                elif msg := check_success_msg(r):
                    st.success(msg)
                    st.session_state["prev_llm_model"] = llm_model

        index_prompt = {
            "LLM 对话": "llm_chat",
            "自定义Agent问答": "agent_chat",
            "搜索引擎问答": "search_engine_chat",
            "法律知识问答": "knowledge_base_chat",
            "事故调查报告结构化": "accident_report_to_structure",
            "事故调查报告结构化-批量": "accident_report_to_structure_batch"
        }
        prompt_templates_kb_list = list(PROMPT_TEMPLATES[index_prompt[dialogue_mode]].keys())
        prompt_template_name = prompt_templates_kb_list[0]
        if "prompt_template_select" not in st.session_state:
            st.session_state.prompt_template_select = prompt_templates_kb_list[0]   #表示默认使用第一个提示词模板

        def prompt_change():
            text = f"已切换为 {prompt_template_name} 模板。"
            st.toast(text)

        temperature = TEMPERATURE
        history_len = HISTORY_LEN

        def on_kb_change():
            st.toast(f"已加载知识库： {st.session_state.selected_kb}")

        if dialogue_mode == "事故调查报告结构化-批量":
            selected_kb = "temporary_accident"
            kb_top_k = 8
            score_threshold = float(0.5)

    chat_box.output_messages()
    cols = st.columns(1)
    if cols[0].button(
            "点击开始结构化事故调查报告",
            use_container_width=True,
    ):
        folder_path = r"P:\Workspace\Python\zhengwuhangyezhushou3\knowledge_base\accident\content" # 替换
        file_names = os.listdir(folder_path)
        file_paths = [os.path.join(folder_path, file_name) for file_name in file_names]

        number_file = 0
        for file_path in file_paths:
            number_file += 1
            chat_box.ai_say([
                f"正在处理第{number_file}个文件"])
            file_name = os.path.basename(file_path)
            existing_name_df = pd.read_excel('P:\Workspace\Python\zhengwuhangyezhushou3\事故调查报告数据库.xlsx') # 替换
            existing_name_list = existing_name_df['文件名']
            found = False

            for name in existing_name_list:
                if file_name == name:
                    found = True
                    break
            if found:
                chat_box.ai_say([
                    "出现重复文档"])
                continue  # 作用于外部循环
            files = [file_path]
            doc_details = pd.DataFrame(get_kb_file_details(kb))
            file_num = len(doc_details["file_name"])

            for i in range(file_num):
                file_names = [doc_details.loc[i, "file_name"]]
                api.delete_kb_docs(kb, file_names=file_names, delete_content=True)

            ret = api.upload_kb_docs(files,
                                     knowledge_base_name=kb,
                                     override=True,  # 文件名冲突的时候，可以覆盖
                                     chunk_size=chunk_size,
                                     chunk_overlap=chunk_overlap,
                                     zh_title_enhance=zh_title_enhance)
            if msg := check_success_msg(ret):
                st.toast(msg, icon="✔")
            elif msg := check_error_msg(ret):
                st.toast(msg, icon="✖")

            history = get_messages_history(history_len)
            if dialogue_mode == "事故调查报告结构化-批量":
                labels_answer = {}
                with open(r'P:\Workspace\Python\zhengwuhangyezhushou3\事故标签体系问题集.csv', encoding='gb2312') as f:  # 替换
                    reader = list(csv.reader(f))
                    label_number = 0
                    x = 1
                    for row in reader:
                        text_out = ""
                        while row[-1] == '':
                            row.pop(-1)
                        if is_choose_label[label_number]:
                            prompt = f"{row[1]}"
                            x = x+1
                            text = f"《{row[0]}》"
                            for d in api.knowledge_base_chat(prompt,
                                                             knowledge_base_name=selected_kb,
                                                             top_k=kb_top_k,
                                                             score_threshold=score_threshold,
                                                             history=history,
                                                             model=llm_model,
                                                             prompt_name=prompt_template_name,
                                                             temperature=temperature):
                                if error_msg := check_error_msg(d):
                                    st.error(error_msg)
                                elif chunk := d.get("answer"):
                                    text += chunk
                                    text_out += chunk
                            labels_answer[f"{row[0]}"] = text_out.replace("", "").replace(" ", "")
                            docs = search_docs(query=prompt, knowledge_base_name=selected_kb, top_k=kb_top_k, score_threshold=score_threshold)
                            sorted_docs = sorted(docs, key=lambda x: abs(x.score), reverse=False)
                        label_number += 1

                base_name = os.path.splitext(file_name)[0]
                elements = base_name.split('-')
                labels_answer['事故发生时间'] = elements[0] if len(elements) > 0 else "出现错误，请认真核对"
                labels_answer['行业领域'] = elements[1] if len(elements) > 1 else "出现错误，请认真核对"
                labels_answer['事故类型'] = elements[2] if len(elements) > 2 else "出现错误，请认真核对"
                labels_answer['事故等级'] = elements[3] if len(elements) > 3 else "出现错误，请认真核对"
                doc_list = get_kb_file_details(kb)
                for dl in doc_list:
                    file_name_value = dl.get('file_name')
                    labels_answer[f"文件名"]=file_name_value
                existing_df = pd.read_excel('P:\Workspace\Python\zhengwuhangyezhushou3\事故调查报告数据库.xlsx')  # 替换
                new_row = pd.Series(labels_answer)
                existing_df = pd.concat([existing_df, new_row.to_frame().T], ignore_index=True)
                existing_df.to_excel('P:\Workspace\Python\zhengwuhangyezhushou3\事故调查报告数据库.xlsx', index=False)  # 替换


        chat_box.ai_say([
            f"处理完毕，共{number_file}个文件"])


    now = datetime.now()

    class CustomChatBox(ChatBox):

        def custom_export2md(self, chat_name=None):
            lines = []
            history = self.other_history(chat_name)
            for idx, msg in enumerate(history):
                role = msg["role"]
                content = [f"{role}: {e.content}  \n" for e in msg["elements"]]
                text = " ".join(content)
                if idx > 0 and history[idx - 1]["role"] == "assistant":
                    lines.append("\n\n")

                text_without_links = re.sub(r'\([^)]*\/[^)]*\)', '', text)
                lines.append(text_without_links)
            return lines

        def transfer_content(self, old_chat_box):
            old_history = old_chat_box.history

            added_messages = set()
            for msg in old_history:
                role = msg["role"]
                elements = msg["elements"]
                metadata = msg["metadata"]
                msg_key = tuple(elements)
                if msg_key in added_messages:
                    continue
                added_messages.add(msg_key)

    custom_chat_box = CustomChatBox()
    custom_chat_box.transfer_content(chat_box)

    with st.sidebar:
        export_btn = st.columns(1)
        export_btn[0].download_button(
            "导出记录",
            "".join(custom_chat_box.custom_export2md()),
            file_name=f"{now:%Y-%m-%d %H.%M}_对话记录.doc",
            mime="text/markdown",
            use_container_width=True,
        )
