"""该模块实现政务行业AI助手的对话界面功能，主要包含：
1. 基于Streamlit构建的Web对话界面
2. 支持多种对话模式：应急处置方案推荐、法律知识问答等
3. 集成大模型对话、知识库检索、搜索引擎问答等功能
4. 提供对话历史记录管理和导出功能
5. 支持不同LLM模型的动态切换和配置"""

# 导入依赖库
import streamlit as st  # 网页应用框架
from webui_pages.utils import *  # 自定义工具函数
from streamlit_chatbox import *  # 聊天框组件
from datetime import datetime  # 时间处理
import os  # 文件路径处理
from configs import (LLM_MODEL, TEMPERATURE, HISTORY_LEN, PROMPT_TEMPLATES,
                     DEFAULT_KNOWLEDGE_BASE, DEFAULT_SEARCH_ENGINE,LANGCHAIN_LLM_MODEL)  # 配置参数
from typing import List, Dict  # 类型提示
from rank_bm25 import BM25Okapi  # BM25检索算法
import jieba  # 中文分词
import re  # 正则表达式

# 初始化聊天框组件
chat_box = ChatBox(
    user_avatar=os.path.join("img", "user-icon.png"),  # 用户头像路径
    assistant_avatar=os.path.join("img", "assistant-icon.png")  # 助手头像路径
)

def get_messages_history(history_len: int, content_in_expander: bool = False) -> List[Dict]:
    '''
    获取 streamlit_chatbox 组件的历史记录，整理成 List[Dict] 形式。
    其中，Assistant 的消息会移除 Markdown 标题。
    '''
    def filter(msg):
        # 仅保留 markdown 和 text 类型的元素
        content = [x for x in msg["elements"] if x._output_method in ["markdown", "text"]]
        # 可选：是否包含折叠框内的内容
        if not content_in_expander:
            content = [x for x in content if not x._in_expander]
        # 提取元素内容
        content = [x.content for x in content]
        
        # 合并内容
        content_text = "\n\n".join(content)
        
        # 获取角色
        role = msg.get("role", "")

        # 对 Assistant 的消息内容进行清洗，移除可能导致解析错误的 Markdown 标题
        if role == "assistant":
            # 移除 Markdown 标题 (###, ##, #)
            content_text = re.sub(r"(?m)^#+\s+.*\n?", "", content_text)
            # 移除粗体和斜体标记，但保留内容
            content_text = re.sub(r"\*\*([^*]+)\*\*", r"\1", content_text)
            content_text = re.sub(r"\*([^*]+)\*", r"\1", content_text)
            # 移除列表标记，但保留内容
            content_text = re.sub(r"(?m)^[-\*+]\s+", "", content_text)
            # 移除多余的换行符
            content_text = re.sub(r"\n{3,}", "\n\n", content_text).strip()

        # 确保只返回 'user' 或 'assistant' 角色，并且 content 是字符串
        if role in ["user", "assistant"] and isinstance(content_text, str):
            return {
                "role": role,
                "content": content_text,
            }
        else:
            return None # 过滤掉不符合格式或角色的消息

    # 使用 filter 函数过滤历史记录
    history = chat_box.filter_history(history_len=history_len, filter=filter)
    # 再次过滤掉 filter 函数返回 None 的结果
    valid_history = [msg for msg in history if msg is not None]
    return valid_history


def get_default_llm_model(api: ApiRequest) ->  Tuple[str, bool]:
    """获取默认的LLM模型
    具体而言，内部流程：
    1. 获取正在运行的模型列表
    2. 如果配置的默认模型在运行中，则返回该模型
    3. 如果配置的默认模型不在运行中，则返回第一个本地模型
    4. 如果本地模型不存在，则返回第一个在线模型
    5. 如果所有模型都不在运行中，则返回空字符串和False
    6. 返回值是一个元组，包含模型名称和是否本地模型

    外部调用：
    1. dialogue_page新建一个聊天会话
    2. 获取默认模型

    Args:
        api (ApiRequest): API请求对象
    
    Returns:
        tuple: (模型名称, 是否本地模型)
    """
    running_models = api.list_running_models()  # 获取正在运行的模型列表
    if not running_models:  # 无运行中的模型
        return "", False

    if LLM_MODEL in running_models:  # 优先使用配置的默认模型
        return LLM_MODEL, True

    local_models = [k for k, v in running_models.items() if not v.get("online_api")]  # 筛选本地模型
    if local_models:  # 存在本地模型时优先选择
        return local_models[0], True
    return list(running_models)[0], False  # 默认返回第一个可用模型
    # 示例返回值：
    # ("gpt-3.5-turbo", False)


def dialogue_page(api: ApiRequest):
    """对话页面主函数
    
    Args:
        api (ApiRequest): API请求对象
    """
    # 初始化聊天会话
    if not chat_box.chat_inited:
        
        default_model = get_default_llm_model(api)[0]  # 获取默认模型
        st.toast("欢迎使用 政务行业AI助手2.0 ! \n\n当前模型API: " + default_model)  # 显示欢迎提示
        chat_box.init_session()  # 初始化聊天会话
    
    # 侧边栏配置区域
    with st.sidebar:
        def on_mode_change():
            """对话模式切换回调函数
            
            当用户在对话模式选择框中选择不同的模式时触发。
            更新界面提示信息，并重置聊天历史。
            """
            mode = st.session_state.dialogue_mode  # 获取当前选择的模式
            text = f"已切换到 {mode} 模式。"
            # 法律问答模式显示当前知识库
            if mode == "法律知识问答":
                cur_kb = st.session_state.get("selected_kb")
                if cur_kb:
                    text = f"{text} 当前知识库： `{cur_kb}`。"
            st.toast(text)  # 显示提示信息
            chat_box.reset_history()  # 重置聊天历史


        dialogue_mode = st.selectbox(
            "请选择对话模式：",
            ["应急处置方案推荐",
             "法律知识问答",
            # 不需要、也用不了的的问答模式，暂时注释
            #  "Agent问答",
            #  "搜索引擎问答",
            #  "事故调查报告检索"
            ],
            index=1, # 默认选中索引
            on_change=on_mode_change, # 切换回调，当用户选择不同的对话模式时，会调用on_mode_change函数 TODO:深入理解
            key="dialogue_mode", # 会话状态键，用于保存当前选中的对话模式
        )


        def on_llm_change():
            """模型切换回调函数

            当用户在模型选择框中选择不同的模型时触发。
            更新当前模型的会话状态，并在必要时切换模型。
            """
            if llm_model:
                config = api.get_model_config(llm_model)  # 获取模型配置
                # 仅本地模型需要记录前一个模型
                if not config.get("online_api"):  
                    st.session_state["prev_llm_model"] = llm_model
                st.session_state["cur_llm_model"] = st.session_state.llm_model

        def llm_model_format_func(x):
            """模型显示格式处理函数
            
            处理模型显示格式，标记运行中的模型。
            """
            return f"{x} (Running)" if x in running_models else x  # 标记运行中的模型

        # 模型选择相关逻辑
        running_models = list(api.list_running_models())  # 正在运行的模型列表
        running_models += LANGCHAIN_LLM_MODEL.keys()  # 添加LangChain模型
        available_models = []  # 可用模型列表
        config_models = api.list_config_models()  # 获取配置的所有模型
        
        # 处理worker模型
        worker_models = list(config_models.get("worker", {}))
        for m in worker_models:
            if m not in running_models and m != "default":
                available_models.append(m)
        
        # 处理在线API模型
        for k, v in config_models.get("online", {}).items():
            if not v.get("provider") and k not in running_models:
                available_models.append(k)
        
        # 处理LangChain模型
        for k, v in config_models.get("langchain", {}).items():
            available_models.append(k)
        
        llm_models = running_models + available_models  # 合并可用模型列表
        index = llm_models.index(st.session_state.get("cur_llm_model", get_default_llm_model(api)[0]))
        llm_model = LLM_MODEL  # 当前选择的模型
        
        # 模型切换处理逻辑
        if (st.session_state.get("prev_llm_model") != llm_model
                and not llm_model in config_models.get("online", {})
                and not llm_model in config_models.get("langchain", {})
                and llm_model not in running_models):
            with st.spinner(f"正在加载模型： {llm_model}，请勿进行操作或刷新页面"):
                prev_model = st.session_state.get("prev_llm_model")  # 获取前一个模型
                r = api.change_llm_model(prev_model, llm_model)  # 调用API切换模型
                if msg := check_error_msg(r):  # 错误处理
                    st.error(msg)
                elif msg := check_success_msg(r):  # 成功处理
                    st.success(msg)
                    st.session_state["prev_llm_model"] = llm_model  # 更新会话状态

        # 提示模板配置
        index_prompt = {
            "应急处置方案推荐": "llm_chat",
            "自定义Agent问答": "agent_chat",
            "搜索引擎问答": "search_engine_chat",
            "法律知识问答": "knowledge_base_chat",
            "事故调查报告检索": "knowledge_base_chat",
        }
        prompt_templates_kb_list = list(PROMPT_TEMPLATES[index_prompt[dialogue_mode]].keys())  # 获取对应模式的提示模板
        prompt_template_name = prompt_templates_kb_list[0]  # 默认模板
        prompt_template_name_shigu = prompt_templates_kb_list[1]  # 事故调查专用模板
        
        # 初始化提示模板选择状态
        if "prompt_template_select" not in st.session_state:
            st.session_state.prompt_template_select = prompt_templates_kb_list[0]

        def prompt_change():
            """提示模板切换回调
            
            当用户在提示模板选择框中选择不同的模板时触发。
            更新界面提示信息，并重置聊天历史。
            """
            text = f"已切换为 {prompt_template_name} 模板。"
            st.toast(text)

        # 获取配置参数
        temperature = TEMPERATURE  # 温度参数
        history_len = HISTORY_LEN  # 历史记录长度

        def on_kb_change():
            """知识库切换回调
            
            当用户在知识库选择框中选择不同的知识库时触发。
            更新界面提示信息，并重置聊天历史。
            """
            st.toast(f"已加载知识库： {st.session_state.selected_kb}")

        # 根据不同模式设置参数
        if dialogue_mode == "法律知识问答":
            selected_kb = "law"  # 法律知识库
            kb_top_k = 8  # 检索结果数量
            score_threshold = 1  # 分数阈值
        elif dialogue_mode == "事故调查报告检索":
            selected_kb = "accident"  # 事故知识库
            kb_top_k = 3
            score_threshold = 1
        elif dialogue_mode == "搜索引擎问答":
            # 搜索引擎配置
            search_engine_list = api.list_search_engines()  # 获取可用搜索引擎列表
            if DEFAULT_SEARCH_ENGINE in search_engine_list:  # 设置默认搜索引擎
                index = search_engine_list.index(DEFAULT_SEARCH_ENGINE)
            else:
                index = search_engine_list.index("duckduckgo") if "duckduckgo" in search_engine_list else 0
            
            # 搜索引擎参数配置展开面板
            with st.expander("搜索引擎配置", True):
                search_engine = st.selectbox(
                    label="请选择搜索引擎",
                    options=search_engine_list,
                    index=index,
                )
                se_top_k = st.number_input("匹配搜索结果条数：", 1, 20, SEARCH_ENGINE_TOP_K)  # 结果数量设置

    # 渲染历史聊天消息
    chat_box.output_messages()

    # 聊天输入框背景文字提示
    chat_input_placeholder = "请输入对话内容，换行请使用Shift+Enter "
    
    # 处理用户输入
    if prompt := st.chat_input(chat_input_placeholder, key="prompt"):
        history = get_messages_history(history_len)  # 获取历史记录
        chat_box.user_say(prompt)  # 显示用户消息
        
        # 应急处置方案推荐模式
        if dialogue_mode == "应急处置方案推荐":
            chat_box.ai_say("正在思考...")  # 显示助手状态
            text = ""
            # 调用聊天API
            r = api.chat_chat(prompt,
                            history=history,
                            model=llm_model,
                            prompt_name=prompt_template_name,
                            temperature=temperature)
            # 流式处理响应
            for t in r:
                if error_msg := check_error_msg(t):  # 错误检查
                    st.error(error_msg)
                    break
                text += t  # 拼接响应内容
                chat_box.update_msg(text)  # 更新消息
            chat_box.update_msg(text, streaming=False)  # 最终更新消息

        # 自定义Agent问答模式
        elif dialogue_mode == "自定义Agent问答":
            chat_box.ai_say([
                f"正在思考...",
                Markdown("...", in_expander=True, title="思考过程", state="complete"),  # 可展开的思考过程
            ])
            text = ""
            ans = ""
            # 支持Agent的模型列表
            support_agent = ["Azure-OpenAI", "OpenAI", "Anthropic", "Qwen", "qwen-api", "baichuan-api"]  
            # 检查模型兼容性
            if not any(agent in llm_model for agent in support_agent):
                ans += "正在思考... \n\n <span style='color:red'>该模型并没有进行Agent对齐，请更换支持Agent的模型获得更好的体验！</span>\n\n\n"
                chat_box.update_msg(ans, element_index=0, streaming=False)  # 更新错误提示
            
            # 处理Agent聊天响应
            for d in api.agent_chat(prompt,
                                  history=history,
                                  model=llm_model,
                                  prompt_name=prompt_template_name,
                                  temperature=temperature):
                try:
                    d = json.loads(d)  # 解析JSON响应
                except:
                    pass
                if error_msg := check_error_msg(d):  # 错误处理
                    st.error(error_msg)
                if chunk := d.get("answer"):  # 中间答案
                    text += chunk
                    chat_box.update_msg(text, element_index=1)
                if chunk := d.get("final_answer"):  # 最终答案
                    ans += chunk
                    chat_box.update_msg(ans, element_index=0)
                if chunk := d.get("tools"):  # 使用工具
                    text += "\n\n".join(d.get("tools", []))
                    chat_box.update_msg(text, element_index=1)
            # 最终更新消息
            chat_box.update_msg(ans, element_index=0, streaming=False)
            chat_box.update_msg(text, element_index=1, streaming=False)

        # 法律知识问答模式
        elif dialogue_mode == "法律知识问答":
            chat_box.ai_say([
                "正在思考...",
                Markdown("...", in_expander=True, title="建议参考的法律条文", state="complete"),  # 法律条文展示
            ])
            text = ""
            # 调用知识库聊天API
            for d in api.knowledge_base_chat(prompt,
                                           knowledge_base_name=selected_kb,
                                           top_k=kb_top_k,
                                           score_threshold=score_threshold,
                                           history=history,
                                           model=llm_model,
                                           prompt_name=prompt_template_name,
                                           temperature=temperature):
                if error_msg := check_error_msg(d):  # 错误处理
                    st.error(error_msg)
                elif chunk := d.get("answer"):  # 获取答案片段
                    text += chunk
                    chat_box.update_msg(text, element_index=0)  # 更新答案

            # 处理检索到的文档
            docs = d.get("docs", [])
            print("999999999999999999")
            print(docs)
            text_contents = []
            # 解析文档内容
            for doc in docs:
                parts = doc.split('参考: ')
                if len(parts) > 1:
                    text_content = parts[1]
                    text_contents.append(text_content)

            # 空内容检查
            if not text_contents:  
                st.error("未提取到有效的文档内容，请检查知识库或输入的查询。")
                return

            # 中文分词处理
            tokenized_texts = [list(jieba.cut(text_content)) for text_content in text_contents if text_content.strip()]
            tokenized_texts = [text for text in tokenized_texts if text]  # 过滤空内容
            
            if not tokenized_texts:  # 空分词检查
                st.error("知识库中没有有效内容，无法进行 BM25 检索。")
                return

            # BM25相关性排序
            tokenized_query = list(jieba.cut(text))  # 查询分词
            bm25 = BM25Okapi(tokenized_texts, b=1, k1=1)  # 初始化BM25
            scores = bm25.get_scores(tokenized_query)  # 计算相关性分数
            docs_with_scores = [(doc, scores[index]) for index, doc in enumerate(docs)]  # 组合文档与分数
            sorted_docs_with_scores = sorted(docs_with_scores, key=lambda x: x[1], reverse=True)  # 降序排序
            sorted_docs = [doc for doc, score in sorted_docs_with_scores]  # 提取排序后的文档
            formatted_docs = [doc for doc, score in sorted_docs_with_scores]  # 格式化结果
            
            # 更新聊天消息
            chat_box.update_msg(text, element_index=0, streaming=False)
            chat_box.update_msg("\n\n".join(formatted_docs), element_index=1, streaming=False)

        # 事故调查报告检索模式
        elif dialogue_mode == "事故调查报告检索":
            chat_box.ai_say([
                "检索中……",
                Markdown("...", in_expander=False, title="事故调查报告匹配结果", state="complete"),  # 直接显示结果
                "检索完成（检索结果相关度由高到低排序）"
            ])
            text = ""
            # 调用事故调查专用API
            for d in api.knowledge_base_chat_shigu(prompt,
                                                knowledge_base_name=selected_kb,
                                                top_k=kb_top_k,
                                                score_threshold=score_threshold,
                                                history=history,
                                                model=llm_model,
                                                # prompt_name=prompt_template_name,
                                                prompt_name=prompt_template_name_shigu,  #LI 配合上面理解不了的索引使用，没太搞懂
                                                temperature=temperature):
                if error_msg := check_error_msg(d):  # check whether error occured
                    st.error(error_msg)
            # 更新文档显示
            chat_box.update_msg("\n\n".join(d.get("docs", [])), element_index=1, streaming=False)

        # 搜索引擎问答模式
        elif dialogue_mode == "搜索引擎问答":
            chat_box.ai_say([
                f"正在执行 `{search_engine}` 搜索...",
                Markdown("...", in_expander=True, title="网络搜索结果", state="complete"),  # 可展开的搜索结果
            ])
            text = ""
            # 处理搜索引擎响应
            for d in api.search_engine_chat(prompt,
                                          search_engine_name=search_engine,
                                          top_k=se_top_k,
                                          history=history,
                                          model=llm_model,
                                          prompt_name=prompt_template_name,
                                          temperature=temperature):
                if error_msg := check_error_msg(d):  # 错误处理
                    st.error(error_msg)
                elif chunk := d.get("answer"):  # 获取答案片段
                    text += chunk
                    chat_box.update_msg(text, element_index=0)  # 更新答案
            # 最终更新消息
            chat_box.update_msg(text, element_index=0, streaming=False)
            chat_box.update_msg("\n\n".join(d.get("docs", [])), element_index=1, streaming=False)

    # 导出功能相关
    now = datetime.now()  # 获取当前时间

    class CustomChatBox(ChatBox):
        """自定义聊天框组件，扩展导出功能"""
        def custom_export2md(self, chat_name=None):
            """自定义导出为Markdown格式
            
            Args:
                chat_name (str): 聊天名称
            
            Returns:
                list: 格式化后的消息行列表
            """
            lines = []
            history = self.other_history(chat_name)  # 获取历史记录
            for idx, msg in enumerate(history):
                role = msg["role"]  # 消息角色
                content = [f"{role}: {e.content}  \n" for e in msg["elements"]]  # 格式化内容
                text = " ".join(content)
                # 添加消息间换行
                if idx > 0 and history[idx - 1]["role"] == "assistant":
                    lines.append("\n\n")

                text_without_links = re.sub(r'\([^)]*\/[^)]*\)', '', text)  # 移除链接
                lines.append(text_without_links)
            return lines

        def transfer_content(self, old_chat_box):
            """转移聊天内容
            
            Args:
                old_chat_box (ChatBox): 原始聊天框对象
            """
            old_history = old_chat_box.history  # 获取原始历史记录
            added_messages = set()  # 去重集合
            for msg in old_history:
                role = msg["role"]
                elements = msg["elements"]
                metadata = msg["metadata"]
                msg_key = tuple(elements)  # 生成消息唯一标识
                if msg_key in added_messages:  # 跳过已添加消息
                    continue
                added_messages.add(msg_key)

    # 初始化自定义聊天框并转移内容
    custom_chat_box = CustomChatBox()
    custom_chat_box.transfer_content(chat_box)
    
    # 导出按钮
    with st.sidebar:
        export_btn = st.columns(1)
        export_btn[0].download_button(
            "导出记录",
            "".join(custom_chat_box.custom_export2md()),  # 生成导出内容
            file_name=f"{now:%Y-%m-%d %H.%M}_对话记录.doc",  # 动态文件名
            mime="text/markdown",  # MIME类型
            use_container_width=True,  # 自适应宽度
        )
