import streamlit as st
from webui_pages.utils import *
from streamlit_chatbox import *
from datetime import datetime
import os
from configs import (LLM_MODEL, TEMPERATURE, HISTORY_LEN, PROMPT_TEMPLATES,
                     DEFAULT_KNOWLEDGE_BASE, DEFAULT_SEARCH_ENGINE,LANGCHAIN_LLM_MODEL)
from typing import List, Dict
import csv
from rank_bm25 import BM25Okapi
import jieba
import re

chat_box = ChatBox(
    user_avatar=os.path.join(
        "img",
        "user-icon.png"
    ),
    assistant_avatar=os.path.join(
        "img",
        "assistant-icon.png"
    )
)
def get_messages_history(history_len: int, content_in_expander: bool = False) -> List[Dict]:

    def filter(msg):
        content = [x for x in msg["elements"] if x._output_method in ["markdown", "text"]]
        if not content_in_expander:
            content = [x for x in content if not x._in_expander]
        content = [x.content for x in content]

        return {
            "role": msg["role"],
            "content": "\n\n".join(content),
        }

    return chat_box.filter_history(history_len=history_len, filter=filter)


def get_default_llm_model(api: ApiRequest) -> (str, bool):

    running_models = api.list_running_models()
    if not running_models:
        return "", False

    if LLM_MODEL in running_models:
        return LLM_MODEL, True

    local_models = [k for k, v in running_models.items() if not v.get("online_api")]
    if local_models:
        return local_models[0], True
    return list(running_models)[0], False


def water_resources_page(api: ApiRequest):

    if not chat_box.chat_inited:
        default_model = get_default_llm_model(api)[0]
        st.toast(
            "欢迎使用 政务行业AI助手2.0 ! \n\n"
        )
        chat_box.init_session()
    with st.sidebar:
        # TODO: 对话模型与会话绑定
        dialogue_mode = "水利私域问答"

        running_models = list(api.list_running_models())
        running_models += LANGCHAIN_LLM_MODEL.keys()
        available_models = []
        config_models = api.list_config_models()
        worker_models = list(config_models.get("worker", {}))  # 仅列出在FSCHAT_MODEL_WORKERS中配置的模型
        for m in worker_models:
            if m not in running_models and m != "default":
                available_models.append(m)
        for k, v in config_models.get("online", {}).items():  # 列出ONLINE_MODELS中直接访问的模型
            if not v.get("provider") and k not in running_models:
                available_models.append(k)
        for k, v in config_models.get("langchain", {}).items():  # 列出LANGCHAIN_LLM_MODEL支持的模型
            available_models.append(k)
        llm_models = running_models + available_models
        index = llm_models.index(st.session_state.get("cur_llm_model", get_default_llm_model(api)[0]))
        llm_model = LLM_MODEL
        if (st.session_state.get("prev_llm_model") != llm_model
                and not llm_model in config_models.get("online", {})
                and not llm_model in config_models.get("langchain", {})
                and llm_model not in running_models):
            with st.spinner(f"正在加载模型： {llm_model}，请勿进行操作或刷新页面"):
                prev_model = st.session_state.get("prev_llm_model")
                r = api.change_llm_model(prev_model, llm_model)
                if msg := check_error_msg(r):
                    st.error(msg)
                elif msg := check_success_msg(r):
                    st.success(msg)
                    st.session_state["prev_llm_model"] = llm_model

        index_prompt = {
            "LLM 对话": "llm_chat",
            "自定义Agent问答": "agent_chat",
            "搜索引擎问答": "search_engine_chat",
            "法律知识问答": "knowledge_base_chat",
            "水利私域问答": "water_resources"
        }
        prompt_templates_kb_list = list(PROMPT_TEMPLATES[index_prompt[dialogue_mode]].keys())
        prompt_template_name = prompt_templates_kb_list[0]
        if "prompt_template_select" not in st.session_state:
            st.session_state.prompt_template_select = prompt_templates_kb_list[0]   #表示默认使用第一个提示词模板

        def prompt_change():
            text = f"已切换为 {prompt_template_name} 模板。"
            st.toast(text)

        temperature = TEMPERATURE
        history_len = HISTORY_LEN

        def on_kb_change():
            st.toast(f"已加载知识库： {st.session_state.selected_kb}")

        if dialogue_mode == "水利私域问答":
            selected_kb = "temporary_water"
            kb_top_k = 8
            score_threshold = 1

    chat_box.output_messages()

    chat_input_placeholder = "请输入对话内容，换行请使用Shift+Enter "

    if prompt := st.chat_input(chat_input_placeholder, key="prompt"):
        history = get_messages_history(history_len)
        chat_box.user_say(prompt)
        if dialogue_mode == "水利私域问答":
            chat_box.ai_say([
                "正在思考...",
                Markdown("...", in_expander=True, title="建议参考的法律条文", state="complete"),
            ])
            text = ""
            for d in api.knowledge_base_chat(prompt,
                                             knowledge_base_name=selected_kb,
                                             top_k=kb_top_k,
                                             score_threshold=score_threshold,
                                             history=history,
                                             model=llm_model,
                                             prompt_name=prompt_template_name,
                                             temperature=temperature):
                if error_msg := check_error_msg(d):  # check whether error occured
                    st.error(error_msg)
                elif chunk := d.get("answer"):
                    text += chunk
                    chat_box.update_msg(text, element_index=0)

            docs = d.get("docs", [])
            text_contents = []
            for doc in docs:
                parts = doc.split('参考: ')
                if len(parts) > 1:
                    text_content = parts[1]
                    text_contents.append(text_content)

            tokenized_texts = [list(jieba.cut(text_content)) for text_content in text_contents if text_content.strip()]
            tokenized_texts = [text for text in tokenized_texts if text]  # 这一行会去除空的列表
            tokenized_query = list(jieba.cut(text))
            bm25 = BM25Okapi(tokenized_texts, b=1, k1=1)
            scores = bm25.get_scores(tokenized_query)
            docs_with_scores = [(doc, scores[index]) for index, doc in enumerate(docs)]
            sorted_docs_with_scores = sorted(docs_with_scores, key=lambda x: x[1], reverse=True)
            sorted_docs = [doc for doc, score in sorted_docs_with_scores]
            formatted_docs = [doc for doc, score in sorted_docs_with_scores]
            chat_box.update_msg(text, element_index=0, streaming=False)
            chat_box.update_msg("\n\n".join(formatted_docs), element_index=1, streaming=False)

    now = datetime.now()

    class CustomChatBox(ChatBox):

        def custom_export2md(self, chat_name=None):
            lines = []
            history = self.other_history(chat_name)
            for idx, msg in enumerate(history):
                role = msg["role"]
                content = [f"{role}: {e.content}  \n" for e in msg["elements"]]
                text = " ".join(content)

                if idx > 0 and history[idx - 1]["role"] == "assistant":
                    lines.append("\n\n")

                text_without_links = re.sub(r'\([^)]*\/[^)]*\)', '', text)

                lines.append(text_without_links)
            return lines

        def transfer_content(self, old_chat_box):
            old_history = old_chat_box.history
            added_messages = set()
            for msg in old_history:
                role = msg["role"]
                elements = msg["elements"]
                metadata = msg["metadata"]
                msg_key = tuple(elements)
                if msg_key in added_messages:
                    continue
                added_messages.add(msg_key)

    custom_chat_box = CustomChatBox()
    custom_chat_box.transfer_content(chat_box)

    with st.sidebar:
        export_btn = st.columns(1)
        export_btn[0].download_button(
            "导出记录",
            "".join(custom_chat_box.custom_export2md()),
            file_name=f"{now:%Y-%m-%d %H.%M}_对话记录.doc",
            mime="text/markdown",
            use_container_width=True,
        )
