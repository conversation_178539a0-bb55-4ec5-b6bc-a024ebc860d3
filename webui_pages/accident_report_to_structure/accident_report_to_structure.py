# 事故调查报告结构化
import streamlit as st
from webui_pages.utils import *
from streamlit_chatbox import *
from datetime import datetime
import os
import time
from configs import (LLM_MODEL, TEMPERATURE, HISTORY_LEN, PROMPT_TEMPLATES,
                     DEFAULT_KNOWLEDGE_BASE, DEFAULT_SEARCH_ENGINE,LANGCHAIN_LLM_MODEL)
from typing import List, Dict
import csv
import re
from server.knowledge_base.kb_service.base import get_kb_details, get_kb_file_details
from server.knowledge_base.utils import get_file_path, LOADER_DICT
import pandas as pd

# 向量库匹配
from server.knowledge_base.kb_doc_api import search_docs

chat_box = ChatBox(
    user_avatar=os.path.join(
        "img",
        "user-icon.png"
    ),
    assistant_avatar=os.path.join(
        "img",
        "assistant-icon.png"
    )
)
def get_messages_history(history_len: int, content_in_expander: bool = False) -> List[Dict]:
    '''
    获取 streamlit_chatbox 组件的历史记录，整理成 List[Dict] 形式。
    其中，Assistant 的消息会移除 Markdown 标题。
    '''
    def filter(msg):
        # 仅保留 markdown 和 text 类型的元素
        content = [x for x in msg["elements"] if x._output_method in ["markdown", "text"]]
        # 可选：是否包含折叠框内的内容
        if not content_in_expander:
            content = [x for x in content if not x._in_expander]
        # 提取元素内容
        content = [x.content for x in content]
        
        # 合并内容
        content_text = "\n\n".join(content)
        
        # 获取角色
        role = msg.get("role", "")

        # 对 Assistant 的消息内容进行清洗，移除可能导致解析错误的 Markdown 标题
        if role == "assistant":
            # 移除 Markdown 标题 (###, ##, #)
            content_text = re.sub(r"(?m)^#+\s+.*\n?", "", content_text)
            # 移除粗体和斜体标记，但保留内容
            content_text = re.sub(r"\*\*([^*]+)\*\*", r"\1", content_text)
            content_text = re.sub(r"\*([^*]+)\*", r"\1", content_text)
            # 移除列表标记，但保留内容
            content_text = re.sub(r"(?m)^[-\*+]\s+", "", content_text)
            # 移除多余的换行符
            content_text = re.sub(r"\n{3,}", "\n\n", content_text).strip()

        # 确保只返回 'user' 或 'assistant' 角色，并且 content 是字符串
        if role in ["user", "assistant"] and isinstance(content_text, str):
            return {
                "role": role,
                "content": content_text,
            }
        else:
            return None # 过滤掉不符合格式或角色的消息

    # 使用 filter 函数过滤历史记录
    history = chat_box.filter_history(history_len=history_len, filter=filter)
    # 再次过滤掉 filter 函数返回 None 的结果
    valid_history = [msg for msg in history if msg is not None]
    return valid_history


def get_default_llm_model(api: ApiRequest) -> tuple[str, bool]:
    running_models = api.list_running_models()
    if not running_models:
        return "", False

    if LLM_MODEL in running_models:
        return LLM_MODEL, True

    local_models = [k for k, v in running_models.items() if not v.get("online_api")]
    if local_models:
        return local_models[0], True
    return list(running_models)[0], False


def accident_report_to_structure_page(api: ApiRequest):
    # 初始化知识库
    kb = "temporary_accident"
    kb_list = {x["kb_name"]: x for x in get_kb_details()}
    st.session_state["selected_kb_info"] = kb_list[kb]['kb_info']
    
    # 确保chat_history存在于session_state
    if "chat_history" not in st.session_state:
        st.session_state.chat_history = []
    
    languages = {
        "CN": {
            "button": "浏览文件",
            "instructions": "将文件拖放到此处",
            "limits": "每个文件限制 200MB",
        },
    }
    lang = "CN"
    hide_label = (
        """
        <style>
            div[data-testid="stFileUploader"]>section[data-testid="stFileUploadDropzone"]>button[data-testid="baseButton-secondary"] {
               color:white;
            }
            div[data-testid="stFileUploader"]>section[data-testid="stFileUploadDropzone"]>button[data-testid="baseButton-secondary"]::after {
                content: "BUTTON_TEXT";
                color:black;
                display: block;
                position: absolute;
            }
            div[data-testid="stFileDropzoneInstructions"]>div>span {
               visibility:hidden;
            }
            div[data-testid="stFileDropzoneInstructions"]>div>span::after {
               content:"INSTRUCTIONS_TEXT";
               visibility:visible;
               display:block;
            }
            div[data-testid="stFileDropzoneInstructions"]>div>small {
               visibility:hidden;
            }
            div[data-testid="stFileDropzoneInstructions"]>div>small::before {
               content:"FILE_LIMITS";
               visibility:visible;
               display:block;
            }
        </style>
        """.replace(
            "BUTTON_TEXT", languages.get(lang).get("button")
        )
        .replace("INSTRUCTIONS_TEXT", languages.get(lang).get("instructions"))
        .replace("FILE_LIMITS", languages.get(lang).get("limits"))
    )
    st.markdown(hide_label, unsafe_allow_html=True)

    # 上传文件
    files = st.file_uploader("上传需要标签化的事故调查报告（文字型的PDF文件）：",
                             [i for ls in LOADER_DICT.values() for i in ls],
                             accept_multiple_files=True,
                             )

    chunk_size = CHUNK_SIZE
    chunk_overlap = OVERLAP_SIZE
    zh_title_enhance = ZH_TITLE_ENHANCE

    # 初始化session_state中的标签列表和选择状态
    if "label_list" not in st.session_state:
        st.session_state.label_list = []
        st.session_state.is_choose_label = []
        # 读取标签数据
        try:
            with open(r'P:\Workspace\Python\zhengwuhangyezhushou3\事故标签体系问题集3.csv', encoding='gb2312') as f:
                reader = list(csv.reader(f))
                for row in reader:
                    while row and row[-1] == '': # 移除行尾的空值
                        row.pop(-1)
                    st.session_state.label_list.append(f"{row[0]}") # 取第一列作为标签名称
                    st.session_state.is_choose_label.append(True) # 默认选中所有标签
            # 这些标签所在行的第三列都是"不需要设计"
            st.session_state.is_choose_label[0] = False # 取消选中第一个标签
            st.session_state.is_choose_label[4] = False # 取消选中第五个标签
            st.session_state.is_choose_label[6] = False # 取消选中第七个标签
            st.session_state.is_choose_label[7] = False # 取消选中第八个标签
        except Exception as e:
            st.error(f"加载标签数据出错: {str(e)}")

    if st.button(
            "添加事故调查报告",
            disabled=len(files) == 0,
    ):
        # 删除已有的文件
        doc_details = pd.DataFrame(get_kb_file_details(kb))
        file_num = 0  # 初始化默认值
        
        # 检查并删除已有文件
        if len(doc_details) > 0:  # 确保有数据
            if 'file_name' in doc_details.columns:  # 检查列名是否存在
                file_num = len(doc_details["file_name"])
                for i in range(file_num):
                    file_names = [doc_details.loc[i, "file_name"]]
                    api.delete_kb_docs(kb, file_names=file_names, delete_content=True)
            else:
                st.warning("文档详情中未找到文件名列")
        else:
            st.info("知识库中暂无文档")
            
        # 上传新文件
        ret = api.upload_kb_docs(files,
                                 knowledge_base_name=kb,
                                 override=True,
                                 chunk_size=chunk_size,
                                 chunk_overlap=chunk_overlap,
                                 zh_title_enhance=zh_title_enhance)
        if msg := check_success_msg(ret):
            st.toast(msg, icon="✔")
        elif msg := check_error_msg(ret):
            st.toast(msg, icon="✖")
            
    # 使用expander选择要拆解的事故要素
    with st.expander(
            "请选择事故调查报告中需提取的要素",
            expanded=True,
    ):
        # 创建全选/取消全选选项
        select_all = st.checkbox("全选/取消全选")

        # 创建3列布局展示标签选择
        cols = st.columns(3)
        for i in range(len(st.session_state.label_list)):
            # 为每个标签创建复选框，绑定到全选状态
            is_selected = cols[i % 3].checkbox(st.session_state.label_list[i], select_all)
            # 更新session state中的选择状态
            st.session_state.is_choose_label[i] = is_selected

    # 初始化聊天框
    if not chat_box.chat_inited:
        default_model = get_default_llm_model(api)[0]
        st.toast(
            "欢迎使用 政务行业AI助手! \n\n"
        )
        chat_box.init_session()
        
    with st.sidebar:
        # 对话模式切换回调
        def on_mode_change():
            mode = st.session_state.dialogue_mode
            text = f"已切换到 {mode} 模式。"
            if mode == "法律知识问答":
                cur_kb = st.session_state.get("selected_kb")
                if cur_kb:
                    text = f"{text} 当前知识库： `{cur_kb}`。"
            st.toast(text)

        dialogue_mode = "事故调查报告结构化"
        
        # LLM模型切换回调
        def on_llm_change():
            if llm_model:
                config = api.get_model_config(llm_model)
                if not config.get("online_api"):  # 只有本地model_worker可以切换模型
                    st.session_state["prev_llm_model"] = llm_model
                st.session_state["cur_llm_model"] = st.session_state.llm_model

        # 模型格式化函数
        def llm_model_format_func(x):
            if x in running_models:
                return f"{x} (Running)"
            return x

        # 获取可用模型
        running_models = list(api.list_running_models())
        running_models += LANGCHAIN_LLM_MODEL.keys()
        available_models = []
        config_models = api.list_config_models()
        worker_models = list(config_models.get("worker", {}))  # 仅列出在FSCHAT_MODEL_WORKERS中配置的模型
        for m in worker_models:
            if m not in running_models and m != "default":
                available_models.append(m)
        for k, v in config_models.get("online", {}).items():  # 列出ONLINE_MODELS中直接访问的模型
            if not v.get("provider") and k not in running_models:
                available_models.append(k)
        for k, v in config_models.get("langchain", {}).items():  # 列出LANGCHAIN_LLM_MODEL支持的模型
            available_models.append(k)
            
        llm_models = running_models + available_models
        index = llm_models.index(st.session_state.get("cur_llm_model", get_default_llm_model(api)[0]))
        llm_model = LLM_MODEL
        
        # 加载模型
        if (st.session_state.get("prev_llm_model") != llm_model
                and not llm_model in config_models.get("online", {})
                and not llm_model in config_models.get("langchain", {})
                and llm_model not in running_models):
            with st.spinner(f"正在加载模型： {llm_model}，请勿进行操作或刷新页面"):
                prev_model = st.session_state.get("prev_llm_model")
                r = api.change_llm_model(prev_model, llm_model)
                if msg := check_error_msg(r):
                    st.error(msg)
                elif msg := check_success_msg(r):
                    st.success(msg)
                    st.session_state["prev_llm_model"] = llm_model

        # 提示词模板索引
        index_prompt = {
            "LLM 对话": "llm_chat",
            "自定义Agent问答": "agent_chat",
            "搜索引擎问答": "search_engine_chat",
            "法律知识问答": "knowledge_base_chat",
            "事故调查报告结构化": "accident_report_to_structure"
        }
        
        # 选择提示词模板
        prompt_templates_kb_list = list(PROMPT_TEMPLATES[index_prompt[dialogue_mode]].keys())
        prompt_template_name = prompt_templates_kb_list[0]
        if "prompt_template_select" not in st.session_state:
            st.session_state.prompt_template_select = prompt_templates_kb_list[0]   #表示默认使用第一个提示词模板

        # 提示词模板切换回调
        def prompt_change():
            text = f"已切换为 {prompt_template_name} 模板。"
            st.toast(text)

        # 设置温度和历史长度
        temperature = TEMPERATURE
        history_len = HISTORY_LEN

        # 知识库切换回调
        def on_kb_change():
            st.toast(f"已加载知识库： {st.session_state.selected_kb}")

        # 事故调查报告结构化模式特定设置
        if dialogue_mode == "事故调查报告结构化":
            selected_kb = "temporary_accident"
            kb_top_k = 8
            score_threshold = float(0.5)

    # 显示聊天框
    chat_box.output_messages()
    
    # 开始结构化按钮
    cols = st.columns(1)
    if cols[0].button(
            "点击开始结构化事故调查报告",
            use_container_width=True,
    ):
        # 显示"开始处理"信息，并记录其索引
        # chat_message_idx = chat_box.ai_say(["开始处理事故调查报告..."]) # ai_say不返回索引
        chat_box.ai_say(["开始处理事故调查报告..."])
        # 获取刚刚添加的消息的索引（它现在是历史记录的最后一条）
        if chat_box.history: # 确保历史记录不为空
             chat_message_idx = len(chat_box.history) - 1
        else:
             st.error("无法获取消息索引，历史记录为空。")
             return # 无法继续处理

        # 获取历史对话记录
        history = get_messages_history(history_len)
        
        # 存储拆解结果
        labels_answer = {}
        
        # 读取事故标签问题集
        try:
            with open(r'P:\Workspace\Python\zhengwuhangyezhushou3\事故标签体系问题集3.csv',
                    encoding='gb2312') as f:
                reader = list(csv.reader(f))
                label_number = 0
                
                # 处理每个标签
                for row in reader:
                    try:
                        # 移除行末空值
                        while row and row[-1] == '':
                            row.pop(-1)
                            
                        # 检查是否选中了该标签
                        if st.session_state.is_choose_label[label_number]:
                            # 初始化提示词
                            prompt = ''
                            current_label = row[0]
                            
                            # 构建提示词
                            for row_k in row[1:]:
                                if row_k:  # 确保不添加空字符串
                                    prompt = prompt + "&" + row_k
                            
                            # 去除开头可能存在的&符号
                            if prompt.startswith('&'):
                                prompt = prompt[1:]
                            
                            # 更新状态提示，不显示详细内容
                            chat_box.update_msg(f"正在分析: {current_label}...", history_index=chat_message_idx, element_index=0)
                            
                            text_out = ""
                            try:
                                # 调用知识库问答API，收集完整回答
                                for d in api.knowledge_base_chat(prompt,
                                                            knowledge_base_name=selected_kb,
                                                            top_k=kb_top_k,
                                                            score_threshold=score_threshold,
                                                            history=history, # 传入清理过的历史记录
                                                            model=llm_model,
                                                            prompt_name=prompt_template_name,
                                                            temperature=temperature):
                                    if error_msg := check_error_msg(d):
                                        st.error(error_msg)
                                    elif chunk := d.get("answer"):
                                        text_out += chunk
                                        # 不再在此处调用 update_msg 显示流式结果
                                        # time.sleep(0.1) # 如果需要减速可以保留
                            except Exception as e:
                                st.error(f"处理标签 {current_label} 时出错: {str(e)}")
                                continue

                            # 存储拆解结果 (去除可能存在的HTML标签)
                            labels_answer[current_label] = re.sub(r'<.*?>', '', text_out).strip()
                        
                        # 无论是否处理该标签，都需要增加标签索引
                        label_number += 1
                    except Exception as e:
                        st.error(f"处理标签行时出错: {str(e)}")
                        continue
        except Exception as e:
            st.error(f"读取标签文件时出错: {str(e)}")

        try:
            # 获取文件名并提取信息
            folder_path = os.path.join("knowledge_base", "temporary_accident", "content")
            
            # 检查文件夹是否存在
            if os.path.exists(folder_path) and os.path.isdir(folder_path):
                files = os.listdir(folder_path)
                if files:  # 确保文件夹不为空
                    file_name = files[0]  # 获取唯一文件名
                    base_name = os.path.splitext(file_name)[0]
                    elements = base_name.split('-')
                    
                    # 从文件名中提取信息
                    labels_answer['事故发生时间'] = elements[0] if len(elements) > 0 else "出现错误，请认真核对"
                    labels_answer['行业领域'] = elements[1] if len(elements) > 1 else "出现错误，请认真核对"
                    labels_answer['事故类型'] = elements[2] if len(elements) > 2 else "出现错误，请认真核对"
                    labels_answer['事故等级'] = elements[3] if len(elements) > 3 else "出现错误，请认真核对"
                    
                    # 生成结果摘要
                    result_summary = "# 事故调查报告结构化结果\n\n"
                    
                    # 首先添加基本信息
                    result_summary += "## 基本信息\n\n"
                    basic_info = ['事故发生时间', '行业领域', '事故类型', '事故等级']
                    for key in basic_info:
                        if key in labels_answer:
                            result_summary += f"- **{key}**: {labels_answer[key]}\n"
                    result_summary += "\n"
                    
                    # 然后添加详细分析结果
                    result_summary += "## 详细分析\n\n"
                    for key, value in labels_answer.items():
                        if key not in basic_info:  # 跳过已经显示的基本信息
                            result_summary += f"### {key}\n\n{value}\n\n"
                    
                    # 更新初始消息为最终报告内容
                    chat_box.update_msg(result_summary, history_index=chat_message_idx, element_index=0)
                else:
                    error_msg = "未找到任何文件，请先上传事故调查报告"
                    st.error(error_msg)
                    # 更新初始消息为错误信息
                    chat_box.update_msg(error_msg, history_index=chat_message_idx, element_index=0)
            else:
                error_msg = f"文件夹路径不存在，请确保已正确上传文件"
                st.error(error_msg)
                # 更新初始消息为错误信息
                chat_box.update_msg(error_msg, history_index=chat_message_idx, element_index=0)
        except Exception as e:
            error_msg = f"处理文件或生成报告时出错: {str(e)}"
            st.error(error_msg)
             # 更新初始消息为错误信息
            chat_box.update_msg(error_msg, history_index=chat_message_idx, element_index=0)

    now = datetime.now()

    class CustomChatBox(ChatBox):
        def custom_export2md(self, chat_name=None):
            lines = []
            try:
                history = self.other_history(chat_name)
                for idx, msg in enumerate(history):
                    role = msg["role"]
                    # 为不同角色添加不同的标题级别
                    if role == "assistant":
                        content = [f"## AI分析结果\n\n{e.content}\n\n" for e in msg["elements"]]
                    else:
                        content = [f"## 用户输入\n\n{e.content}\n\n" for e in msg["elements"]]
                    
                    text = "".join(content)
                    # 移除链接
                    text_without_links = re.sub(r'\([^)]*\/[^)]*\)', '', text)
                    # 确保段落之间有空行
                    text_without_links = re.sub(r'\n{3,}', '\n\n', text_without_links)
                    lines.append(text_without_links)
            except Exception as e:
                lines.append(f"导出记录时出错: {str(e)}")
            return lines

        def transfer_content(self, old_chat_box):
            try:
                if hasattr(old_chat_box, 'history'):
                    old_history = old_chat_box.history
                    added_messages = set()
                    for msg in old_history:
                        if 'role' in msg and 'elements' in msg:
                            role = msg["role"]
                            elements = msg["elements"]
                            if hasattr(msg, 'metadata'):
                                metadata = msg["metadata"]
                            else:
                                metadata = {}
            except Exception as e:
                st.error(f"传输聊天内容时出错: {str(e)}")

    custom_chat_box = CustomChatBox()
    custom_chat_box.transfer_content(chat_box)
    
    with st.sidebar:
        export_btn = st.columns(1)
        export_btn[0].download_button(
            "导出记录",
            "".join(custom_chat_box.custom_export2md()),
            file_name=f"{now:%Y-%m-%d %H.%M}_对话记录.md",
            mime="text/markdown",
            use_container_width=True,
        )