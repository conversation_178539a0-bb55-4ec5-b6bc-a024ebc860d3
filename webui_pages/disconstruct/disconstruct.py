"""
应急预案文件拆解模块

本模块实现了应急预案文件的上传、解析和拆解功能。
主要功能包括：
1. 上传应急预案文件（支持PDF等格式）
2. 选择需要拆解的预案要素
3. 使用大语言模型对预案文件进行智能拆解
4. 导出拆解结果

整体流程：用户上传文件 -> 选择拆解要素 -> 系统调用LLM进行分析 -> 展示拆解结果 -> 可导出结果
"""
# 应急预案文件拆解
import streamlit as st  # 导入Streamlit库，用于构建Web界面
from webui_pages.utils import *  # 导入工具函数
from streamlit_chatbox import *  # 导入聊天框组件
from datetime import datetime  # 导入日期时间处理模块
import os  # 导入操作系统模块，用于文件路径处理
from configs import (LLM_MODEL, TEMPERATURE, HISTORY_LEN, PROMPT_TEMPLATES,
                     DEFAULT_KNOWLEDGE_BASE, DEFAULT_SEARCH_ENGINE,LANGCHAIN_LLM_MODEL)  # 导入配置参数
from typing import List, Dict, Tuple  # 导入类型注解
import csv  # 导入CSV文件处理模块
from server.knowledge_base.kb_service.base import get_kb_details, get_kb_file_details  # 导入知识库服务函数
from server.knowledge_base.utils import get_file_path, LOADER_DICT  # 导入知识库工具函数
import pandas as pd  # 导入pandas数据处理库

from server.knowledge_base.kb_doc_api import search_docs  # 导入文档搜索API
import re  # 导入正则表达式模块


# 定义应急预案文件拆解提示词路径
DISCONSTRUCT_PROMPT_PATH = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), "prompt_templates","应急预案文件拆解2.csv")

# 初始化聊天框组件，设置用户和助手的头像
chat_box = ChatBox(
    user_avatar=os.path.join(
        "img",
        "user-icon.png"
    ),
    assistant_avatar=os.path.join(
        "img",
        "assistant-icon.png"
    )
)

def get_messages_history(history_len: int, content_in_expander: bool = False) -> List[Dict]:
    '''
    获取 streamlit_chatbox 组件的历史记录，整理成 List[Dict] 形式。
    其中，Assistant 的消息会移除 Markdown 标题。
    '''
    def filter(msg):
        # 仅保留 markdown 和 text 类型的元素
        content = [x for x in msg["elements"] if x._output_method in ["markdown", "text"]]
        # 可选：是否包含折叠框内的内容
        if not content_in_expander:
            content = [x for x in content if not x._in_expander]
        # 提取元素内容
        content = [x.content for x in content]
        
        # 合并内容
        content_text = "\n\n".join(content)
        
        # 获取角色
        role = msg.get("role", "")

        # 对 Assistant 的消息内容进行清洗，移除可能导致解析错误的 Markdown 标题
        if role == "assistant":
            # 移除 Markdown 标题 (###, ##, #)
            content_text = re.sub(r"(?m)^#+\s+.*\n?", "", content_text)
            # 移除粗体和斜体标记，但保留内容
            content_text = re.sub(r"\*\*([^*]+)\*\*", r"\1", content_text)
            content_text = re.sub(r"\*([^*]+)\*", r"\1", content_text)
            # 移除列表标记，但保留内容
            content_text = re.sub(r"(?m)^[-\*+]\s+", "", content_text)
            # 移除多余的换行符
            content_text = re.sub(r"\n{3,}", "\n\n", content_text).strip()

        # 确保只返回 'user' 或 'assistant' 角色，并且 content 是字符串
        if role in ["user", "assistant"] and isinstance(content_text, str):
            return {
                "role": role,
                "content": content_text,
            }
        else:
            return None # 过滤掉不符合格式或角色的消息

    # 使用 filter 函数过滤历史记录
    history = chat_box.filter_history(history_len=history_len, filter=filter)
    # 再次过滤掉 filter 函数返回 None 的结果
    valid_history = [msg for msg in history if msg is not None]
    return valid_history


def get_default_llm_model(api: ApiRequest) -> Tuple[str, bool]:
    """
    获取默认的大语言模型
    
    Args:
        api: API请求对象
    
    Returns:
        模型名称和是否为本地模型的标志
    """
    running_models = api.list_running_models()  # 获取正在运行的模型列表
    if not running_models:
        return "", False  # 如果没有运行中的模型，返回空字符串和False

    if LLM_MODEL in running_models:
        return LLM_MODEL, True  # 如果配置的默认模型在运行，返回该模型和True

    # 查找本地运行的模型
    local_models = [k for k, v in running_models.items() if not v.get("online_api")]
    if local_models:
        return local_models[0], True  # 如果有本地模型，返回第一个本地模型和True
    return list(running_models)[0], False  # 否则返回第一个运行中的模型和False


def disconstruct_page(api: ApiRequest):
    """
    应急预案文件拆解页面的主函数
    
    Args:
        api: API请求对象，用于与后端服务交互
    """
    kb = "temporary_disconstruct"  # 配置该页面使用的知识库 temporary_disconstruct
    kb_list = {x["kb_name"]: x for x in get_kb_details()}  # 获取所有知识库详情并转为字典
    st.session_state["selected_kb_info"] = kb_list[kb]['kb_info']  # 将选中的知识库信息存入会话状态
    
    # 定义界面语言选项
    languages = {
        "CN": {
            "button": "浏览文件",
            "instructions": "将文件拖放到此处",
            "limits": "每个文件限制 200MB",
        },
    }
    lang = "CN"  # 设置当前语言为中文
    
    # 自定义文件上传组件的CSS样式
    hide_label = (
        """
        <style>
            div[data-testid="stFileUploader"]>section[data-testid="stFileUploadDropzone"]>button[data-testid="baseButton-secondary"] {
               color:white;
            }
            div[data-testid="stFileUploader"]>section[data-testid="stFileUploadDropzone"]>button[data-testid="baseButton-secondary"]::after {
                content: "BUTTON_TEXT";
                color:black;
                display: block;
                position: absolute;
            }
            div[data-testid="stFileDropzoneInstructions"]>div>span {
               visibility:hidden;
            }
            div[data-testid="stFileDropzoneInstructions"]>div>span::after {
               content:"INSTRUCTIONS_TEXT";
               visibility:visible;
               display:block;
            }
            div[data-testid="stFileDropzoneInstructions"]>div>small {
               visibility:hidden;
            }
            div[data-testid="stFileDropzoneInstructions"]>div>small::before {
               content:"FILE_LIMITS";
               visibility:visible;
               display:block;
            }
        </style>
        """.replace(
            "BUTTON_TEXT", languages.get(lang).get("button")  # 替换按钮文本
        )
        .replace("INSTRUCTIONS_TEXT", languages.get(lang).get("instructions"))  # 替换说明文本
        .replace("FILE_LIMITS", languages.get(lang).get("limits"))  # 替换文件限制文本
    )
    st.markdown(hide_label, unsafe_allow_html=True)  # 应用自定义样式

    # 创建文件上传组件
    files = st.file_uploader("上传需要拆解的预案文件（文字型的PDF文件）：",
                             [i for ls in LOADER_DICT.values() for i in ls],    # 支持上传哪些文件，这里不用修改（已经失效）
                             # 这个是是否只是多文件上传，我们只需要一次分析一个文件，因此这里应该设置为false，但是设置为false会报错，因此还是设置为True
                             accept_multiple_files=True, 
                            #  accept_multiple_files=False,
                             )

    # 设置文档分块参数
    chunk_size = CHUNK_SIZE  # 分块大小
    chunk_overlap = OVERLAP_SIZE  # 分块重叠大小
    zh_title_enhance = ZH_TITLE_ENHANCE  # 中文标题增强

    # 添加预案文件 按钮
    """
    点击"添加预案文件"按钮后的工作流程:
    1. 检查是否有文件上传,如果没有则禁用按钮
    2. 获取当前知识库中已有的文件详情
    3. 检查知识库中是否有文件:
       - 如果有文件,则遍历删除所有已有文件
       - 如果没有文件,显示提示信息
    4. 将新上传的文件添加到知识库:
       - 设置文件名冲突时可覆盖
       - 使用指定的分块大小和重叠大小
       - 启用中文标题增强
    5. 检查上传结果并显示对应提示信息
    """
    if st.button(
            "添加预案文件",
            disabled=len(files) == 0,  # 如果没有上传文件则禁用按钮
    ):
        doc_details = pd.DataFrame(get_kb_file_details(kb))  # 获取知识库文件详情
        file_num = 0  # 初始化默认值
        # file_num = len(doc_details["file_name"])
        # 调试，检查并删除已有文件
        if len(doc_details) > 0:  # 确保有数据
            if 'file_name' in doc_details.columns:  # 检查列名是否存在
                file_num = len(doc_details["file_name"])  # 获取文件数量
                
                # 删除知识库中的所有文件
                for i in range(file_num):
                    file_names = [doc_details.loc[i, "file_name"]]
                    api.delete_kb_docs(kb, file_names=file_names, delete_content=True)
            else:
                st.warning("文档详情中未找到文件名列")  # 显示警告信息
        else:
            st.info("知识库中暂无文档")  # 显示提示信息

        # 上传新文件到知识库
        ret = api.upload_kb_docs(files,
                                 knowledge_base_name=kb,
                                 override=True,    # 文件名冲突的时候，可以覆盖
                                 chunk_size=chunk_size,
                                 chunk_overlap=chunk_overlap,
                                 zh_title_enhance=zh_title_enhance)
        # 检查上传结果并显示提示
        if msg := check_success_msg(ret):
            st.toast(msg, icon="✔")
        elif msg := check_error_msg(ret):
            st.toast(msg, icon="✖")

    # 创建可折叠区域，用于选择需要拆解的要素
    with st.expander(
            "请选择应急预案文件中需要拆解的要素",
            expanded=True,  # 默认展开
    ):
        label_list = []  # 存储标签列表
        is_choose_label = []  # 存储标签是否被选中
        
        # 从CSV文件读取预案要素标签
        with open(DISCONSTRUCT_PROMPT_PATH, encoding='gb2312') as f:  # 替换
            reader = list(csv.reader(f))  # 读取CSV文件内容
            for row in reader:
                # 移除行末空值
                while row[-1] == '':
                    row.pop(-1)
                label_list.append(f"{row[0]}")  # 添加标签名称
                is_choose_label.append(False)  # 初始化为未选中

        # 添加全选/取消全选复选框
        select_all = st.checkbox("全选/取消全选")

        # 创建3列布局显示标签复选框
        cols = st.columns(3)
        for i in range(label_list.__len__()):
            is_choose_label[i] = cols[i % 3].checkbox(label_list[i], select_all)  # 创建复选框并绑定全选状态

    # 初始化聊天框
    if not chat_box.chat_inited:
        default_model = get_default_llm_model(api)[0]  # 获取默认模型
        st.toast(
            "欢迎使用 政务行业AI助手! \n\n"  # 显示欢迎信息
        )
        chat_box.init_session()  # 初始化聊天会话
        
    # 创建侧边栏
    with st.sidebar:
        # 定义模式切换回调函数
        def on_mode_change():
            mode = st.session_state.dialogue_mode
            text = f"已切换到 {mode} 模式。"
            if mode == "法律知识问答":
                cur_kb = st.session_state.get("selected_kb")
                if cur_kb:
                    text = f"{text} 当前知识库： `{cur_kb}`。"
            st.toast(text)

        dialogue_mode = "预案文件拆解"  # 设置对话模式

        # 定义LLM模型切换回调函数
        def on_llm_change():
            if llm_model:
                config = api.get_model_config(llm_model)
                if not config.get("online_api"):
                    st.session_state["prev_llm_model"] = llm_model
                st.session_state["cur_llm_model"] = st.session_state.llm_model

        # 定义模型格式化函数，显示模型运行状态
        def llm_model_format_func(x):
            if x in running_models:
                return f"{x} (Running)"
            return x

        # 获取可用的模型列表
        running_models = list(api.list_running_models())  # 获取正在运行的模型
        running_models += LANGCHAIN_LLM_MODEL.keys()  # 添加LangChain模型
        available_models = []  # 可用但未运行的模型
        config_models = api.list_config_models()  # 获取配置的模型
        
        # 获取worker配置的模型
        worker_models = list(config_models.get("worker", {}))  # 仅列出在FSCHAT_MODEL_WORKERS中配置的模型
        for m in worker_models:
            if m not in running_models and m != "default":
                available_models.append(m)
                
        # 获取在线模型
        for k, v in config_models.get("online", {}).items():  # 列出ONLINE_MODELS中直接访问的模型
            if not v.get("provider") and k not in running_models:
                available_models.append(k)
                
        # 获取LangChain支持的模型
        for k, v in config_models.get("langchain", {}).items():  # 列出LANGCHAIN_LLM_MODEL支持的模型    
            available_models.append(k)
            
        # 合并所有模型列表
        llm_models = running_models + available_models
        
        # 获取当前选中的模型索引
        index = llm_models.index(st.session_state.get("cur_llm_model", get_default_llm_model(api)[0]))
        llm_model = LLM_MODEL  # 使用配置的默认模型
        
        # 如果模型变更且不是在线模型或LangChain模型，则加载模型
        if (st.session_state.get("prev_llm_model") != llm_model
                and not llm_model in config_models.get("online", {})
                and not llm_model in config_models.get("langchain", {})
                and llm_model not in running_models):
            with st.spinner(f"正在加载模型： {llm_model}，请勿进行操作或刷新页面"):
                prev_model = st.session_state.get("prev_llm_model")
                r = api.change_llm_model(prev_model, llm_model)  # 切换模型
                if msg := check_error_msg(r):
                    st.error(msg)  # 显示错误信息
                elif msg := check_success_msg(r):
                    st.success(msg)  # 显示成功信息
                    st.session_state["prev_llm_model"] = llm_model  # 更新前一个模型记录

        # 定义不同对话模式对应的提示词模板索引
        index_prompt = {
            "LLM 对话": "llm_chat",
            "自定义Agent问答": "agent_chat",
            "搜索引擎问答": "search_engine_chat",
            "法律知识问答": "knowledge_base_chat",
            "预案文件拆解": "disconstruct"
        }
        
        # 获取当前对话模式的提示词模板列表
        prompt_templates_kb_list = list(PROMPT_TEMPLATES[index_prompt[dialogue_mode]].keys())
        prompt_template_name = prompt_templates_kb_list[0]  # 选择第一个模板
        
        # 初始化提示词模板选择状态
        if "prompt_template_select" not in st.session_state:
            st.session_state.prompt_template_select = prompt_templates_kb_list[0]   #表示默认使用第一个提示词模板

        # 定义提示词模板切换回调函数
        def prompt_change():
            text = f"已切换为 {prompt_template_name} 模板。"
            st.toast(text)

        # 设置温度参数和历史长度
        temperature = TEMPERATURE  # 使用配置的温度参数
        history_len = HISTORY_LEN  # 使用配置的历史长度

        # 定义知识库切换回调函数
        def on_kb_change():
            st.toast(f"已加载知识库： {st.session_state.selected_kb}")

        # 如果是预案文件拆解模式，设置知识库参数
        if dialogue_mode == "预案文件拆解":
            selected_kb = "temporary_disconstruct"  # 使用临时知识库
            kb_top_k = 8  # 设置检索结果数量
            score_threshold = float(1)  # 设置相似度阈值

    # 显示聊天框内容
    chat_box.output_messages()
    
    """
    预案文件拆解工作流程:
    1. 用户在界面上选择要拆解的预案要素(通过is_choose_label列表记录选择状态)
    2. 点击"点击开始应急预案文件拆解"按钮触发拆解流程
    3. 系统工作流程:
       3.1 获取历史对话记录(get_messages_history)用于上下文
       3.2 读取预案要素标签和提示词配置文件(CSV)
       3.3 对每个选中的要素标签:
           - 获取对应的提示词模板
           - 调用knowledge_base_chat API进行拆解
           - API参数包含:提示词、知识库名称、检索数量、相似度阈值等
           - API返回的结果流式输出到聊天框
       3.4 对拆解结果:
           - 存储到labels_answer字典
           - 调用search_docs搜索相关文档
           - 按相似度排序文档
           - 更新聊天框显示
    4. 整个过程中:
       - 使用chat_box控制界面交互
       - 实时显示处理进度
       - 错误处理和异常提示
       - 结果格式化和展示    
    """
    # 创建开始拆解按钮
    cols = st.columns(1)
    if cols[0].button(
            "点击开始应急预案文件拆解",
            use_container_width=True,
    ):
        history = get_messages_history(history_len)  # 获取聊天历史
        if dialogue_mode == "预案文件拆解":
            labels_answer = {}  # 存储拆解结果
            
            # 从CSV文件读取预案要素标签和提示词
            with open(DISCONSTRUCT_PROMPT_PATH, encoding='gb2312') as f:  # 替换
                reader = list(csv.reader(f))
                label_number = 0  # 标签索引
                x = 1  # 计数器
                
                # 处理每一行
                for row in reader:
                    # 移除行末空值
                    while row[-1] == '':
                        row.pop(-1)
                        
                    # 检查是否选中了这个标签
                    if is_choose_label[label_number]:
                        prompt = f"{row[1]}"  # 获取提示词
                        x = x+1  # 增加计数器
                        
                        # 显示处理中的提示
                        chat_box.ai_say([
                            "..."])
                            
                        # 初始化结果文本
                        text = f"《{row[0]}》"
                        
                        # 调用知识库问答API进行拆解
                        for d in api.knowledge_base_chat(prompt,
                                                         knowledge_base_name=selected_kb,
                                                         top_k=kb_top_k,
                                                         score_threshold=score_threshold,
                                                         history=history,
                                                         model=llm_model,
                                                         prompt_name=prompt_template_name,
                                                         temperature=temperature):
                            if error_msg := check_error_msg(d):  # 检查是否发生错误
                                st.error(error_msg)
                            elif chunk := d.get("answer"):  # 获取回答内容
                                text += chunk  # 累加回答内容
                                chat_box.update_msg(text.replace(" ", ""), element_index=0)  # 更新显示内容
                                
                        # 存储拆解结果
                        labels_answer[f"{row[0]}"] = text.replace("", "").replace(" ", "")
                        
                        # 搜索相关文档
                        docs = search_docs(query=prompt, knowledge_base_name=selected_kb, top_k=kb_top_k, score_threshold=score_threshold)
                        sorted_docs = sorted(docs, key=lambda x: abs(x.score), reverse=False)  # 按相似度排序
                        
                        # 更新最终结果
                        chat_box.update_msg(text, element_index=0, streaming=False)
                    
                    # 移动到下一个标签
                    label_number += 1

    # 获取当前时间，用于导出文件命名
    now = datetime.now()

    # 定义自定义聊天框类，用于导出功能
    class CustomChatBox(ChatBox):
        """
        自定义聊天框类，扩展了导出功能
        """
        def custom_export2md(self, chat_name=None):
            """
            自定义导出为Markdown格式的方法
            
            Args:
                chat_name: 聊天名称，默认为None
                
            Returns:
                导出的Markdown格式文本行列表
            """
            lines = []
            history = self.other_history(chat_name)  # 获取历史记录
            for idx, msg in enumerate(history):
                role = msg["role"]  # 获取角色
                content = [f"{role}: {e.content}  \n" for e in msg["elements"]]  # 格式化内容
                text = " ".join(content)  # 合并内容
                if idx > 0 and history[idx - 1]["role"] == "assistant":
                    lines.append("\n\n")  # 在助手回复后添加空行
                    
                # 移除链接
                text_without_links = re.sub(r'\([^)]*\/[^)]*\)', '', text)

                lines.append(text_without_links)  # 添加处理后的文本
            return lines

        def transfer_content(self, old_chat_box):
            """
            从旧聊天框转移内容到新聊天框
            
            Args:
                old_chat_box: 旧的聊天框对象
            """
            old_history = old_chat_box.history  # 获取旧聊天框的历史记录
            added_messages = set()  # 用于跟踪已添加的消息
            for msg in old_history:
                role = msg["role"]  # 获取角色
                elements = msg["elements"]  # 获取元素
                metadata = msg["metadata"]  # 获取元数据
                msg_key = tuple(elements)  # 创建消息键
                if msg_key in added_messages:
                    continue  # 跳过已添加的消息
                added_messages.add(msg_key)  # 标记消息为已添加

    # 创建自定义聊天框实例
    custom_chat_box = CustomChatBox()
    custom_chat_box.transfer_content(chat_box)  # 转移内容

    # 在侧边栏添加导出按钮
    with st.sidebar:
        export_btn = st.columns(1)
        export_btn[0].download_button(
            "导出记录",  # 按钮文本
            "".join(custom_chat_box.custom_export2md()),  # 导出内容
            file_name=f"{now:%Y-%m-%d %H.%M}_对话记录.doc",  # 文件名
            mime="text/markdown",  # MIME类型
            use_container_width=True,  # 使用容器宽度
        )