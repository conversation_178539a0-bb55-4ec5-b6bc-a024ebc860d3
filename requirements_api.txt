langchain>=0.0.319
langchain-experimental>=0.0.30
fschat[model_worker]==0.2.31
xformers==0.0.22 #xformers>=0.0.22.post4
openai>=0.28.1
sentence_transformers>=2.2.2
transformers>=4.34
torch==2.0.1 # torch>=2.1
torchvision
torchaudio
fastapi>=0.104
nltk~=3.8.1
uvicorn~=0.23.1
starlette~=0.27.0
pydantic~=1.10.11
unstructured[all-docs]>=0.10.4
python-magic-bin; sys_platform == 'win32'
SQLAlchemy==2.0.19
faiss-cpu
accelerate
spacy
PyMuPDF==1.22.5
rapidocr_onnxruntime>=1.3.2
requests
pathlib
pytest
scikit-learn
numexpr

vllm>=0.2.0; sys_platform == "linux"


# online api libs
# zhipuai
# dashscope>=1.10.0 # qwen
# qianfan
# volcengine>=1.0.106 # fangzhou
# duckduckgo-searchd #duckduckgo搜索

# uncomment libs if you want to use corresponding vector store
# pymilvus==2.1.3 # requires milvus==2.1.3
# psycopg2
# pgvector